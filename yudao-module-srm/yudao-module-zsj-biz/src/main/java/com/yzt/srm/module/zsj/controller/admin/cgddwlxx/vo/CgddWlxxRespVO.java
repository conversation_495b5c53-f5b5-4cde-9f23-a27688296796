package com.yzt.srm.module.zsj.controller.admin.cgddwlxx.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 采购订单物料信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CgddWlxxRespVO {

    @Schema(description = "自增长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14797")
    @ExcelProperty("自增长ID")
    private Integer id;

    @Schema(description = "主表ID", example = "24244")
    @ExcelProperty("主表ID")
    private Integer mainid;

    @Schema(description = "请购单号")
    @ExcelProperty("请购单号")
    private String qgdh;

    @Schema(description = "名称/零部件")
    @ExcelProperty("名称/零部件")
    private String mcLbj;

    @Schema(description = "图号")
    @ExcelProperty("图号")
    private String th;

    @Schema(description = "图号附件")
    @ExcelProperty("图号附件")
    private Map<String,Object> thfj;

    @Schema(description = "材质")
    @ExcelProperty("材质")
    private String cz;

    @Schema(description = "规格尺寸")
    @ExcelProperty("规格尺寸")
    private String ggcc;

    @Schema(description = "模具名")
    @ExcelProperty("模具名")
    private String mjm;

    @Schema(description = "材料工艺")
    @ExcelProperty("材料工艺")
    private String clgy;

    @Schema(description = "商标")
    @ExcelProperty("商标")
    private String sb;

    @Schema(description = "单重")
    @ExcelProperty("单重")
    private BigDecimal dz;

    @Schema(description = "数量")
    @ExcelProperty("数量")
    private BigDecimal sl;

    @Schema(description = "重量小计")
    @ExcelProperty("重量小计")
    private BigDecimal zlxj;

    @Schema(description = "单位")
    @ExcelProperty("单位")
    private String dw;

    @Schema(description = "材料单价")
    @ExcelProperty("材料单价")
    private BigDecimal cldj;

    @Schema(description = "单价")
    @ExcelProperty("单价")
    private BigDecimal dj;

    @Schema(description = "价格小计")
    @ExcelProperty("价格小计")
    private BigDecimal jgxj;

    @Schema(description = "交货时间")
    @ExcelProperty("交货时间")
    private String jhsj;

    @Schema(description = "取价")
    @ExcelProperty("取价")
    private String qj;

    @Schema(description = "备注说明")
    @ExcelProperty("备注说明")
    private String bzsm;

    @Schema(description = "项目号")
    @ExcelProperty("项目号")
    private String xmh;

    @Schema(description = "规格型号")
    @ExcelProperty("规格型号")
    private String ggxh;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String sbmc;

    @Schema(description = "项目")
    @ExcelProperty("项目")
    private String xm;

    @Schema(description = "标准")
    @ExcelProperty("标准")
    private String bz;

    @Schema(description = "加工项目")
    @ExcelProperty("加工项目")
    private String jgxm;

    @Schema(description = "模具类型")
    @ExcelProperty("模具类型")
    private String mjlx;

    @Schema(description = "工艺")
    @ExcelProperty("工艺")
    private String gy;

    @Schema(description = "工单号")
    @ExcelProperty("工单号")
    private String gdh;

    @Schema(description = "面积(d㎡)")
    @ExcelProperty("面积(d㎡)")
    private BigDecimal mj;

    @Schema(description = "订单项次")
    @ExcelProperty("订单项次")
    private String ddxc;

    @Schema(description = "主体")
    @ExcelProperty("主体")
    private String zt;

    @Schema(description = "阀芯")
    @ExcelProperty("阀芯")
    private String fx;

    @Schema(description = "阀杆")
    @ExcelProperty("阀杆")
    private String fg;

    @Schema(description = "阀座")
    @ExcelProperty("阀座")
    private String fz;

    @Schema(description = "密封面(芯/座)")
    @ExcelProperty("密封面(芯/座)")
    private String mfm;

    @Schema(description = "垫片/填料")
    @ExcelProperty("垫片/填料")
    private String dpTl;

    @Schema(description = "螺丝/螺母")
    @ExcelProperty("螺丝/螺母")
    private String lsLm;

    @Schema(description = "设计标准")
    @ExcelProperty("设计标准")
    private String sjbz;

    @Schema(description = "结构长度")
    @ExcelProperty("结构长度")
    private String jgcd;

    @Schema(description = "连接标准")
    @ExcelProperty("连接标准")
    private String ljbz;

    @Schema(description = "品号")
    @ExcelProperty("品号")
    private String ph;

    @Schema(description = "木箱编号")
    @ExcelProperty("木箱编号")
    private String mxbh;

    @Schema(description = "请购单来源")
    @ExcelProperty("请购单来源")
    private Integer qgdly;


    @Schema(description = "计划日期")
    @ExcelProperty("计划日期")
    private String jhrq;

    @Schema(description = "供应商回复交期")
    @ExcelProperty("供应商回复交期")
    private String hfjq;

    @Schema(description = "特殊要求")
    @ExcelProperty("特殊要求")
    private String tsyq;

    @Schema(description = "销售订单号")
    @ExcelProperty("销售订单号")
    private String xsddh;

    @Schema(description = "销售项次")
    @ExcelProperty("销售项次")
    private Integer xsxc;

    @Schema(description = "部件代码")
    @ExcelProperty("部件代码")
    private String bjdm;

    @Schema(description = "已送货数量")
     @ExcelProperty("已送货数量")
    private BigDecimal yshs;

    @Schema(description = "已入库数量")
     @ExcelProperty("已入库数量")
    private BigDecimal yrks;

    @Schema(description = "请购单项次")
     @ExcelProperty("请购单项次")
    private Integer qgdxc;

    /**
     * 可交货数量
     */
    private BigDecimal kjhs;

    /**
     * 退货数量
     */
    private BigDecimal ths;

    /**
     * 最大交货数量
     */
    private BigDecimal maxjhs;

    /**
     * 采购订单号
     */
    private String cgddh;

    /**
     * 本次送货数
     */
    private BigDecimal bcshs;

    private String cgdlx;

    private BigDecimal sjshs;

    private String lh;

    private String gysdm;

    private String gysmc;

    private String dm;

    private String mc;
}