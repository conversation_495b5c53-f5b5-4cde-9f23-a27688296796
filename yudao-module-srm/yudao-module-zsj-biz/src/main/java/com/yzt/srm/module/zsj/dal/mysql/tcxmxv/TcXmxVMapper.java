package com.yzt.srm.module.zsj.dal.mysql.tcxmxv;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yzt.srm.module.zsj.dal.dataobject.tcxmxv.TcXmxVDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Slave // 宇道框架多数据源注解，指定使用Oracle数据源
@Mapper
public interface TcXmxVMapper extends BaseMapper<TcXmxVDO> {

    @Select("SELECT 项目编号 as prjno, TC_XMXX02 as prjname from TC_XMX_V")
    List<TcXmxVDO> selectAll();
}
