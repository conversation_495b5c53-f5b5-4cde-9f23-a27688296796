package com.yzt.srm.module.workflow.api.wfshdshxxapi.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 送货单明细表 送货信息 DO
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WfShdShxxDto {

    /**
     * 自增长id
     */
    private Long id;
    /**
     * 送货单id
     */
    private Long mainTableId;
    /**
     * 采购订单号
     */
    private String cgddh;
    /**
     * 行号
     */
    private String hh;
    /**
     * 物料号
     */
    private String wlh;
    /**
     * 本次送货数量
     */
    private BigDecimal bcshsl;
    /**
     * 炉号
     */
    private String lh;

    private String th;

    private String thbb;
    /**
     * 净重
     */
    private BigDecimal jz;
    /**
     * 订单特殊要求
     */
    private String tsyq;
    /**
     * 备注
     */
    private String bz;

    private String wlms;

    private String cz;

    private String xsddh;

    private String xsddhh;

    private String lhbs;

    private String qsxlh;

    private String jzxlh;

    private String xlhbs;

    /**
     * 订单项次
     */
    private String ddxc;

    /**
     * 品号
     */
    private String ph;

    /**
     * 工艺(工序)
     */
    private String gy;

    /**
     * 材料工艺
     */
    private String clgy;

    /**
     * 工单号
     */
    private String gdh;

    /**
     * 单重
     */
    private BigDecimal dz;

    /**
     * 单位
     */
    private String dw;

    /**
     * 商标
     */
    private String sb;

    /**
     * 数量（采购量）
     */
    private BigDecimal sl;

    /**
     * 模具名
     */
    private String mjm;

    /**
     * 规则尺寸
     */
    private String ggcc;

    private String mcLbj;

    /**
     * 实际送货数量
     */
    private BigDecimal sjshsl;

    /**
     *  开票状态
     * */
    private Integer invoiceStatus;
}