package com.yzt.srm.module.workflow.controller.admin.tzshd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.List;
import javax.validation.constraints.*;
import javax.validation.Valid;

@Schema(description = "管理后台 - 发起送货数调整流程 Request VO")
@Data
public class TzShdCreateReqVO {

    @Schema(description = "供应商代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "供应商代码不能为空")
    private String gysdm;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "供应商名称不能为空")
    private String gysmc;

    @Schema(description = "采购员名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "采购员名称不能为空")
    private String cgyName;

    @Schema(description = "调整明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "调整明细不能为空")
    @Valid
    private List<TzShdShxxSaveReqVO> adjustDetails;
}
