package com.yzt.srm.module.workflow.service.wfShd;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.bpm.api.task.BpmProcessInstanceApi;
import cn.iocoder.yudao.module.bpm.api.task.dto.BpmProcessInstanceCreateReqDTO;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzt.srm.module.workflow.config.QrProperties;
import com.yzt.srm.module.workflow.controller.admin.wfCgdd.vo.WfCgddAndWlxxRespVO;
import com.yzt.srm.module.workflow.controller.admin.wfShd.vo.*;
import com.yzt.srm.module.workflow.controller.admin.wfShdLh.vo.WfShdLhRespVO;
import com.yzt.srm.module.workflow.controller.admin.wfShdLh.vo.WfShdLhSaveReqVO;
import com.yzt.srm.module.workflow.controller.admin.wfShdRt.vo.WfShdRtRespVO;
import com.yzt.srm.module.workflow.controller.admin.wfShdShxx.vo.ShdSl;
import com.yzt.srm.module.workflow.controller.admin.wfShdShxx.vo.WfShdShxxRespVO;
import com.yzt.srm.module.workflow.controller.admin.wfShdShxx.vo.WfShdShxxSaveReqVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yzt.srm.module.workflow.dal.dataobject.wfShd.WfShdDO;
import com.yzt.srm.module.workflow.dal.dataobject.wfShdLh.WfShdLhDO;
import com.yzt.srm.module.workflow.dal.dataobject.wfShdRt.WfShdRtDO;
import com.yzt.srm.module.workflow.dal.dataobject.wfShdShxx.WfShdShxxDO;
import com.yzt.srm.module.workflow.dal.mysql.wfShd.WfShdMapper;
import com.yzt.srm.module.workflow.dal.mysql.wfShdLh.WfShdLhMapper;
import com.yzt.srm.module.workflow.dal.mysql.wfShdRt.WfShdRtMapper;
import com.yzt.srm.module.workflow.dal.mysql.wfShdShxx.WfShdShxxMapper;
import com.yzt.srm.module.workflow.util.HttpClientUtil;
import com.yzt.srm.module.workflow.util.SrmUtils;
import com.yzt.srm.module.zsj.api.cgddapi.CgddExternalApi;
import com.yzt.srm.module.zsj.api.cgddapi.dto.CgddDto;
import com.yzt.srm.module.zsj.api.cgddwlxxapi.CgddWlxxExternalApi;
import com.yzt.srm.module.zsj.controller.admin.cgddwlxx.vo.CgddShdSjShsReqVo;
import com.yzt.srm.module.zsj.dal.dataobject.cgdd.CgddDO;
import com.yzt.srm.module.zsj.dal.dataobject.cgddwlxx.CgddWlxxDO;
import com.yzt.srm.module.zsj.dal.mysql.cgdd.CgddMapper;
import com.yzt.srm.module.zsj.dal.mysql.cgddwlxx.CgddWlxxMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yzt.srm.module.workflow.enums.ErrorCodeConstants.*;

/**
 * 送货单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Transactional(rollbackFor = Exception.class)
public class WfShdServiceImpl implements WfShdService {

    public static final String PROCESS_KEY = "SHD";

    public static final String CODE_RULE = "SHD_LCDH";

    public static final String FURNACE_NO_NUM_URL = "/srm/furnace-no-num";

    public static final String FURNACE_SERIAL_FURNACE_NO = "/srm/serial-furnace-no";

    @Resource
    private QrProperties qrProperties;

    @Resource
    private WfShdMapper wfShdMapper;

    @Resource
    private WfShdLhMapper wfShdLhMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private WfShdRtMapper wfShdRtMapper;

    @Resource
    private WfShdShxxMapper wfShdShxxMapper;

    @Resource
    private BpmProcessInstanceApi processInstanceApi;

    @Resource
    private CgddMapper cgddMapper;

    @Resource
    private CgddWlxxMapper cgddWlxxMapper;

    @Resource
    private RestTemplate restTemplate;

    @Override
    public Long createWfShd(WfShdSaveReqVO createReqVO) {
        // 插入
        WfShdDO wfShd = BeanUtils.toBean(createReqVO, WfShdDO.class);
        wfShdMapper.insert(wfShd);
        // 返回
        return wfShd.getId();
    }

    @Override
    public void updateWfShd(WfShdSaveReqVO updateReqVO) {
        // 校验存在
        validateWfShdExists(updateReqVO.getId());
        // 更新
        WfShdDO updateObj = BeanUtils.toBean(updateReqVO, WfShdDO.class);
        wfShdMapper.updateById(updateObj);
    }

    @Override
    public void deleteWfShd(Long id) {
        // 校验存在
        validateWfShdExists(id);
        // 删除
        wfShdMapper.deleteById(id);
    }

    private void validateWfShdExists(Long id) {
        if (wfShdMapper.selectById(id) == null) {
            throw exception(WF_SHD_NOT_EXISTS);
        }
    }

    @Override
    public WfShdDO getWfShd(Long id) {
        return wfShdMapper.selectById(id);
    }

    @Override
    public PageResult<WfShdDO> getWfShdPage(WfShdPageReqVO pageReqVO) {
        return wfShdMapper.selectPage(pageReqVO);
    }

    @Override
    public Boolean createShd(ShdReqVO createReqVO) {
        WfShdDO shdzb = createReqVO.getShdzb();
        List<WfShdShxxDO> shxx = createReqVO.getShxx();
        List<WfShdLhDO> lhxx = createReqVO.getLhxx();

        //检查采购订单号-行号是否重复
        Map<String, List<WfShdShxxDO>> collect = shxx.stream().collect(Collectors.groupingBy(shd -> shd.getCgddh() + "-" + shd.getHh()));
        for (Map.Entry<String, List<WfShdShxxDO>> entry : collect.entrySet()) {
            if (entry.getValue().size() > 1) {
                throw exception(new ErrorCode(1310110000, "送货明细相同订单项次：" + entry.getKey() + ",请合并成一行！"));
            }
        }

        //检查送货数量
        List<ShdSl> shdKjhslList = wfShdShxxMapper.selectKjhslByCgddHh(shxx);
        for (WfShdShxxDO wfShdShxxDO : shxx) {
            String cgddh = wfShdShxxDO.getCgddh();
            String hh = wfShdShxxDO.getHh();
            BigDecimal bcshsl = wfShdShxxDO.getBcshsl();
            List<ShdSl> shdSls = shdKjhslList.stream().filter(shdSl -> Objects.equals(cgddh, shdSl.getCgddh()) && Objects.equals(hh, shdSl.getHh())).collect(Collectors.toList());
            if (!shdSls.isEmpty()) {
                BigDecimal kjhsl = shdSls.get(0).getKjhsl();
                if (bcshsl.compareTo(kjhsl) > 0) {
                    throw exception(new ErrorCode(1310048002, cgddh + "-" + hh + "，该订单超过可送货数量"));
                }
            }
        }

        //检查炉号标识为Y的 是否有炉号信息
        for (WfShdShxxDO wfShdShxxDO : shxx) {
            String lhbs = wfShdShxxDO.getLhbs();
            String cgddh = wfShdShxxDO.getCgddh();
            String hh = wfShdShxxDO.getHh();
            if (Objects.equals(lhbs, "")) {
                continue;
            }

            List<WfShdLhDO> lhList = lhxx.stream()
                    .filter(wfShdLhDO -> Objects.equals(wfShdLhDO.getCgddh(), cgddh)
                            && Objects.equals(wfShdLhDO.getHh(), hh)).collect(Collectors.toList());
            if (lhList.isEmpty()) {
                throw exception(new ErrorCode(1310140000, cgddh + "-" + hh + "，有炉号要求，请在炉号明细中维护！"));
            }
        }

        //针对同一采购订单行项次相同炉号，不允许拆分成多行
        Map<String, List<WfShdLhDO>> check1 = lhxx.stream()
                .collect(Collectors.groupingBy(lh -> lh.getCgddh() + "-" + lh.getHh() + "-" + lh.getLh()));
        for (Map.Entry<String, List<WfShdLhDO>> entry : check1.entrySet()) {
            String key = entry.getKey();
            List<WfShdLhDO> value = entry.getValue();
            if (value.size() > 1) {
                throw exception(new ErrorCode(1310048000, key + "，存在同一采购订单相同炉号拆分成多笔情况，请合并成一笔！"));
            }
        }

        //判断炉号明细中采购订单-行号材质是否在本次送货内
        Set<String> lhXc = lhxx.stream().map(lh -> lh.getCgddh() + "-" + lh.getHh()).collect(Collectors.toSet());
        Set<String> shXc = shxx.stream().map(lh -> lh.getCgddh() + "-" + lh.getHh()).collect(Collectors.toSet());
        if (!shXc.containsAll(lhXc)) {
            throw exception(new ErrorCode(1310130000, "存在炉号中订单号不在本次送货清单内,请在炉号明细中删除该数据！"));
        }

        // 判断炉号数量与送货单数量
        Map<String, Long> furnaceNumMap = lhxx.stream()
                .collect(Collectors.groupingBy(lh -> lh.getCgddh() + "-" + lh.getHh(), Collectors.summingLong(lh -> lh.getSl().longValue())));
        Map<String, Long> shNumMap = shxx.stream().filter(sh -> Objects.equals(sh.getLhbs(), "Y"))
                .collect(Collectors.groupingBy(lh -> lh.getCgddh() + "-" + lh.getHh(), Collectors.summingLong(lh -> lh.getBcshsl().longValue())));
        for (Map.Entry<String, Long> entry : furnaceNumMap.entrySet()) {
            String xc = entry.getKey(); //采购订单 行号
            Long furnaceNum = entry.getValue();
            Long shNum = shNumMap.get(xc);
            if (!Objects.equals(furnaceNum, shNum)) {
                throw exception(new ErrorCode(1310048000, xc + ",该订单炉号数量与本次送货数量不相等！"));
            }
        }

        //判断本次炉号数量+历史炉号数量<=炉号总数
        Set<String> lhList = lhxx.stream().map(WfShdLhDO::getLh).collect(Collectors.toSet());
        if(lhList.size()!=0)
        {
            List<WfShdLhRespVO> lhRespVOS = wfShdLhMapper.getHistoryLhSl(lhList);    //取历史炉号数量
            for (WfShdLhDO lhDO : lhxx) {
                String lh = lhDO.getLh();
                BigDecimal current = lhDO.getSl();
                BigDecimal total = lhDO.getZs();
                List<WfShdLhRespVO> temp = lhRespVOS.stream().filter(l -> Objects.equals(lh, l.getLh())).collect(Collectors.toList());
                BigDecimal history = new BigDecimal(0);
                if (!temp.isEmpty()) {
                    WfShdLhRespVO wfShdLhRespVO = temp.get(0);
                    history = wfShdLhRespVO.getSl();
                }
                if (current.add(history).compareTo(total) > 0) {
                    throw exception(new ErrorCode(1310150000, lh + ",该炉号超数量！"));
                }
            }
        }

        Long mainTableId = insertOrUpdate(createReqVO);

        //发起流程
        Map<String, Object> processInstanceVariables = new HashMap<>();
//        CgzRespVO cgzRespVO = cgzService.selectByCgz(shdzb.getCgz());
//        Long ckry = cgzRespVO.getCkry();
//        processInstanceVariables.put("ckry", ckry);

        String processInstanceId = processInstanceApi.createProcessInstance(SecurityFrameworkUtils.getLoginUserId(),
                new BpmProcessInstanceCreateReqDTO().setProcessDefinitionKey(PROCESS_KEY)
                        .setVariables(processInstanceVariables).setBusinessKey(String.valueOf(mainTableId)));

        //生成流程单号
//        String lcdh = autoCodeUtil.genSerialCode(CODE_RULE, "");

        //将编号更新到申请表表单中
        wfShdMapper.updateById(new WfShdDO().setId(mainTableId).setProcessInstanceId(processInstanceId)
//                .setLcdh(lcdh)
                .setLczt(BpmTaskStatusEnum.RUNNING.getStatus()));
        return true;
    }

    @Override
    public Long saveShd(ShdReqVO createReqVO) {
        try {
            return insertOrUpdate(createReqVO);
        } catch (Exception e) {
            return -1L;
        }
    }

    private Long insertOrUpdate(ShdReqVO createReqVO) {
        WfShdDO shdzb = createReqVO.getShdzb();
        List<WfShdShxxDO> shxx = createReqVO.getShxx();
        List<WfShdLhDO> lhxx = createReqVO.getLhxx();
        List<WfShdRtDO> rtxx = createReqVO.getRtxx();

        Long mainTableId = shdzb.getId();
        System.out.println(mainTableId);
        //新增
        if (Objects.equals(mainTableId, null)) {
            wfShdMapper.insert(shdzb);
            mainTableId = shdzb.getId();
        } else {
            wfShdMapper.updateById(shdzb);

            wfShdShxxMapper.deleteByMainTableId(mainTableId);
            wfShdLhMapper.deleteByMainTableId(mainTableId);
            wfShdRtMapper.deleteByMainTableId(mainTableId);
        }

        SrmUtils.setMainTableId(shxx, mainTableId);
        wfShdShxxMapper.insertBatch(shxx);

        SrmUtils.setMainTableId(lhxx, mainTableId);
        wfShdLhMapper.insertBatch(lhxx);

        SrmUtils.setMainTableId(rtxx, mainTableId);
        wfShdRtMapper.insertBatch(rtxx);
        return mainTableId;
    }

    @Override
    public ShdRespVO getSaveShd() {
        List<WfShdRespVO> respVOS = wfShdMapper.getSaveShd();
        if (respVOS.isEmpty()) {
            return null;
        }
        WfShdRespVO shdRespVO = respVOS.get(0);
        Long mainTableId = shdRespVO.getId();
        List<WfShdShxxDO> wfShdShxxDOS = wfShdShxxMapper.selectByMainTableId(mainTableId);
        List<WfShdLhDO> wfShdLhDOS = wfShdLhMapper.selectByMainTableId(mainTableId);
        List<WfShdRtDO> wfShdRtDOS = wfShdRtMapper.selectByMainTableId(mainTableId);

        ShdRespVO respVO = new ShdRespVO();
        respVO.setShdzb(shdRespVO);
        respVO.setShxx(BeanUtils.toBean(wfShdShxxDOS, WfShdShxxRespVO.class));
        respVO.setLhxx(BeanUtils.toBean(wfShdLhDOS, WfShdLhRespVO.class));
        respVO.setRtxx(BeanUtils.toBean(wfShdRtDOS, WfShdRtRespVO.class));
        return respVO;
    }

    @Override
    public List<ShdPrintVO> getShdPrint(String lcdh) {
        return wfShdMapper.getShdPrint(lcdh);
    }

    @Override
    public WfShdLhRespVO getLhSl(WfShdShxxSaveReqVO reqVO) {
        List<Map<String, String>> list = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("cgddh", reqVO.getCgddh());
        map.put("hh", reqVO.getHh());
        map.put("lh", reqVO.getLh());
        list.add(map);

        String data = HttpClientUtil.post(qrProperties.getUrl() + FURNACE_NO_NUM_URL, new HashMap<>(), JSON.toJSONString(list), false);
        //检查返回值
        JSONObject jsonObject = JSONObject.parseObject(data);
        Integer code = jsonObject.getInteger("code");
        if (!Objects.equals(code, 0)) {
            throw exception(new ErrorCode(code, jsonObject.getString("msg")));
        }
        //处理返回数据
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        if (jsonArray.isEmpty()) {
            return null;
        }
        JSONObject js = jsonArray.getJSONObject(0);
        WfShdLhRespVO respVO = BeanUtils.toBean(reqVO, WfShdLhRespVO.class);
        BigDecimal qty = js.getBigDecimal("qty");
        if (Objects.equals(qty.intValue(), 0)) {
            throw exception(FURNACE_REPORT_NOT_EXISTS);
        }
        respVO.setZs(qty);
        return respVO;
    }

    @Override
    public List<WfShdLhSaveReqVO> getXlhSl(WfShdShxxSaveReqVO reqVO) {
        String cgddh = reqVO.getCgddh();
        String hh = reqVO.getHh();
        String wlh = reqVO.getWlh();
        String wlms = reqVO.getWlms();
        String qsxlh = reqVO.getQsxlh();
        String jzxlh = Optional.ofNullable(reqVO.getJzxlh()).orElse("");
        List<String> serialNoList = new ArrayList<>();
        if (Objects.equals(jzxlh, "")) {
            serialNoList.add(qsxlh);
        } else {
            String startPrev = qsxlh.replaceAll("\\d+", "");    //序列号起的前缀
            String endPrev = jzxlh.replaceAll("\\d+", "");    //序列号止的前缀
            if (!Objects.equals(startPrev, endPrev)) {
                throw exception(SERIAL_PREV_NOT_EQUAL);
            }
            String startXlhNum = qsxlh.replaceAll("[a-zA-Z]", "");
            int numLength = startXlhNum.length();
            int start = Integer.parseInt(startXlhNum);
            int end = Integer.parseInt(jzxlh.replaceAll("[a-zA-Z]", ""));
            if (start > end) {
                throw exception(SERIAL_START_BIGGER_END);
            }
            for (int i = start; i <= end; i++) {
                String everyXlh = startPrev + generateZeroString(numLength - String.valueOf(i).length()) + i;
                serialNoList.add(everyXlh);
            }
        }

        String params = serialNoList.stream().map(no -> "serialNos=" + no).collect(Collectors.joining("&"));
        String data = restTemplate.getForObject(qrProperties.getUrl() + FURNACE_SERIAL_FURNACE_NO + "?" + params, String.class);

        JSONObject jsonObject = JSONObject.parseObject(data);
        //检查返回值
        Integer code = jsonObject.getInteger("code");
        if (!Objects.equals(code, 0)) {
            throw exception(new ErrorCode(code, jsonObject.getString("msg")));
        }
        //处理返回数据
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        List<String> xlhList = IntStream.range(0, jsonArray.size())
                .mapToObj(jsonArray::getJSONObject)
                .map(jsonObject1 -> jsonObject1.getString("serialNo"))
                .collect(Collectors.toList());
        //判断每一个序列号是否都取到了炉号
        for (String serialNo : serialNoList) {
            if (!xlhList.contains(serialNo)) {
                throw exception(new ErrorCode(1310210000, serialNo + "，该序列号没有查到炉号！"));
            }
        }

        List<WfShdLhSaveReqVO> reqVOS = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject temp = jsonArray.getJSONObject(i);
            String xlh = temp.getString("serialNo");
            String furnaceNo = temp.getString("furnaceNo");
            WfShdLhSaveReqVO wfShdLhSaveReqVO = new WfShdLhSaveReqVO()
                    .setCgddh(cgddh)
                    .setHh(hh)
                    .setWlh(wlh)
                    .setWlms(wlms)
                    .setSl(new BigDecimal(1))
                    .setXlh(xlh)
                    .setLh(furnaceNo);

            //根据炉号取总数
            List<Map<String, String>> furnaceNumList = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("cgddh", cgddh);
            map.put("hh", hh);
            map.put("lh", furnaceNo);
            furnaceNumList.add(map);
            String furnaceNum = HttpClientUtil.post(FURNACE_NO_NUM_URL, new HashMap<>(), JSON.toJSONString(furnaceNumList), false);
            JSONObject furnaceNumObj = JSONObject.parseObject(furnaceNum);
            //检查返回值
            code = furnaceNumObj.getInteger("code");
            if (!Objects.equals(code, 0)) {
                throw exception(new ErrorCode(code, jsonObject.getString("msg")));
            }
            //处理数据
            JSONObject js = furnaceNumObj.getJSONArray("data").getJSONObject(0);
            BigDecimal qty = js.getBigDecimal("qty");
            wfShdLhSaveReqVO.setZs(qty);
            reqVOS.add(wfShdLhSaveReqVO);
        }
        return reqVOS;
    }

    //拼接0，用于生成序列号
    private static String generateZeroString(int length) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < length; i++) {
            str.append("0");
        }
        return str.toString();
    }

    @Override
    public void updateResult(long id, Integer lczt) {
        wfShdMapper.updateById(new WfShdDO().setId(id).setLczt(lczt));
    }

    @Override
    public ShdReqVO getShdInfo(Long id) {
        WfShdDO shd = wfShdMapper.selectOne("id", id);
        List<WfShdShxxDO> shxxDOS = wfShdShxxMapper.selectByMainTableId(id);
        List<WfShdLhDO> lhDOS = wfShdLhMapper.selectByMainTableId(id);
        List<WfShdRtDO> rtDOS = wfShdRtMapper.selectByMainTableId(id);
        return new ShdReqVO().setShdzb(shd)
                .setShxx(shxxDOS)
                .setLhxx(lhDOS)
                .setRtxx(rtDOS);
    }

    @Override
    public WfShdRespVO getMainTableInfoByProcessInstanceId(String processInstanceId) {
        WfShdDO wfShdDO = wfShdMapper.selectOne(new LambdaQueryWrapperX<WfShdDO>()
                .eqIfPresent(WfShdDO::getProcessInstanceId, processInstanceId));
        return BeanUtils.toBean(wfShdDO, WfShdRespVO.class);
    }

    /**
     * 采购订单归档后更新已交货数量
     *
     * @param processInstanceId
     */
    public void updateCgddYjhsl(String processInstanceId) {
        wfShdShxxMapper.updateCgddYjhsl(processInstanceId);
    }

    /**
     *  获取采购订单以及物料信息流程信息
     */
    @Override
    public WfCgddAndWlxxRespVO getWfCgddAndWlxx(String wfcgddId) {

        //1. 设置wf_cgdd数据
        WfShdDO wfShdDO = wfShdMapper.selectOne(new LambdaQueryWrapperX<WfShdDO>().eq(WfShdDO::getId, Long.valueOf(wfcgddId)));
        WfCgddAndWlxxRespVO wfCgddAndWlxxRespVO = BeanUtils.toBean(wfShdDO, WfCgddAndWlxxRespVO.class);

        //2. 设置wf_cgdd_wlxx数据
        List<WfShdShxxDO> wfShdShxxDOS = wfShdShxxMapper.selectList(new LambdaQueryWrapperX<WfShdShxxDO>().eq(WfShdShxxDO::getMainTableId, Long.valueOf(wfcgddId)));
        List<WfShdShxxRespVO> wfCgddWlxxRespVOS = BeanUtils.toBean(wfShdShxxDOS, WfShdShxxRespVO.class);
        wfCgddAndWlxxRespVO.setWlxxs(wfCgddWlxxRespVOS);
        return wfCgddAndWlxxRespVO;
    }

    @Override
    public WfCgddAndWlxxRespVO getDataByProcessInstanceId(String processInstanceId) {
        //1. 设置wf_cgdd数据
        WfShdDO wfShdDO = wfShdMapper.selectOne(new LambdaQueryWrapperX<WfShdDO>().eq(WfShdDO::getProcessInstanceId, processInstanceId));
        WfCgddAndWlxxRespVO wfCgddAndWlxxRespVO = BeanUtils.toBean(wfShdDO, WfCgddAndWlxxRespVO.class);

        //2. 设置wf_cgdd_wlxx数据
        List<WfShdShxxDO> wfShdShxxDOS = wfShdShxxMapper.selectList(new LambdaQueryWrapperX<WfShdShxxDO>().eq(WfShdShxxDO::getMainTableId, Long.valueOf(wfShdDO.getId())));
        List<WfShdShxxRespVO> wfCgddWlxxRespVOS = BeanUtils.toBean(wfShdShxxDOS, WfShdShxxRespVO.class);
        for (int i = 0; i < wfCgddWlxxRespVOS.size(); i++) {
            wfCgddWlxxRespVOS.get(i).setId(Long.valueOf(i+1));
        }
        wfCgddAndWlxxRespVO.setWlxxs(wfCgddWlxxRespVOS);
        Long id = SecurityFrameworkUtils.getLoginUser().getId();
        String nickname = adminUserApi.getUser(id).getNickname();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        wfCgddAndWlxxRespVO.setPrintTime(LocalDateTime.now().format(formatter));
        wfCgddAndWlxxRespVO.setNikeName(nickname);
        wfCgddAndWlxxRespVO.setGysDw(wfShdDO.getGysdm()+" "+wfShdDO.getGysmc());
        return wfCgddAndWlxxRespVO;
    }

    @Override
    public WfShdDO selectByProcessInstanceId(String processInstanceId) {
        return wfShdMapper.selectOne(new LambdaQueryWrapperX<WfShdDO>().eq(WfShdDO::getProcessInstanceId, processInstanceId));
    }
    
    @Override
    public Long insert(WfShdDO wfShdDO) {
        wfShdMapper.insert(wfShdDO);
        return wfShdDO.getId();
    }
    
    @Override
    public void updateById(WfShdDO wfShdDO) {
        wfShdMapper.updateById(wfShdDO);
    }


    /**
     * 更新实际送货数
     */
    @Override
    @Transactional
    public String updataSjshs(List<CgddShdSjShsReqVo> cgddShdSjShsReqVos) {
        List<Long> shxxIds = cgddShdSjShsReqVos.stream().map(CgddShdSjShsReqVo::getId).map(Long::valueOf).collect(Collectors.toList());
        Map<String, CgddShdSjShsReqVo> wfMap = cgddShdSjShsReqVos.stream().collect(Collectors.toMap(CgddShdSjShsReqVo::getId, Function.identity()));
        List<WfShdShxxDO> wfShdShxxDOS = wfShdShxxMapper.selectList(new LambdaQueryWrapperX<WfShdShxxDO>().in(WfShdShxxDO::getId, shxxIds));
        ArrayList<WfShdShxxDO> updateShdShxxList = new ArrayList<>();
        for (WfShdShxxDO wfShdShxxDO : wfShdShxxDOS){
            CgddShdSjShsReqVo cgddShdSjShsReqVo = wfMap.get(String.valueOf(wfShdShxxDO.getId()));
            if (cgddShdSjShsReqVo.getSjshsl().compareTo(cgddShdSjShsReqVo.getBcshsl()) < 0){
                //存在实际送货数小于本次送货数量情况
                //更新zsj中的数据
                String hh = wfShdShxxDO.getHh();  //行号
                String cgddh = wfShdShxxDO.getCgddh(); //采购订单号
                CgddDO cgddDO = cgddMapper.selectOne(new LambdaQueryWrapperX<CgddDO>().eq(CgddDO::getErpCgddh, cgddh));
                BigDecimal chazhi = cgddShdSjShsReqVo.getBcshsl().subtract(cgddShdSjShsReqVo.getSjshsl());
                CgddWlxxDO cgddWlxxDO = cgddWlxxMapper.selectOne(new LambdaQueryWrapperX<CgddWlxxDO>().eq(CgddWlxxDO::getDdxc, hh).eq(CgddWlxxDO::getMainid, cgddDO.getId()));
                cgddWlxxDO.setKjhs(cgddWlxxDO.getKjhs().add(chazhi));
                cgddWlxxMapper.updateById(cgddWlxxDO);
            }
            wfShdShxxDO.setBcshsl(cgddShdSjShsReqVo.getBcshsl());
            wfShdShxxDO.setSjshsl(cgddShdSjShsReqVo.getSjshsl());
            updateShdShxxList.add(wfShdShxxDO);
        }
        if (updateShdShxxList.size() != 0){
            wfShdShxxMapper.updateBatch(updateShdShxxList);
        }

        return "提交成功！";
    }

    /**
     * 根据采购订单号设置采购订单的状态为1
     * @param cgdhList
     */
    @Override
    public void batchUpdateCgddByCgdhList(List<String> cgdhList) {
        List<WfShdShxxDO> wfShdShxxDOS = wfShdShxxMapper.selectList(new LambdaQueryWrapperX<WfShdShxxDO>().in(WfShdShxxDO::getCgddh, cgdhList));
        wfShdShxxDOS.stream().forEach(item ->item.setInvoiceStatus(1));
        wfShdShxxMapper.updateBatch(wfShdShxxDOS);
    }

    @Override
    public PageResult<WfShdDetailRespVO> getWfShdDetailPage(WfShdDetailPageReqVO pageReqVO) {

        // 2. 创建分页对象
        Page<WfShdDetailRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());

        // 3. 执行分页查询
        wfShdShxxMapper.selectWfShdDetailPage(page, pageReqVO);

        // 4. 转换结果
        PageResult<WfShdDetailRespVO> pageResult = new PageResult<>(page.getRecords(), page.getTotal());
        List<WfShdDetailRespVO> shxxList = pageResult.getList();

        List<AdminUserRespDTO> adminUserRespDTOS = adminUserApi.selectAllUser();
        Map<Long, AdminUserRespDTO> userRespDTOMap = adminUserRespDTOS.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, Function.identity()));
        shxxList.stream().forEach(item ->{
            AdminUserRespDTO adminUserRespDTO = userRespDTOMap.get(Long.valueOf(item.getCgy()));
            if (adminUserRespDTO != null){
                item.setCgyName(adminUserRespDTO.getNickname());
            }
        });
        return new PageResult<>(shxxList, pageResult.getTotal());
    }
}