package com.yzt.srm.module.workflow.controller.admin.tzshd.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 送货数调整明细新增/修改 Request VO")
@Data
public class TzShdShxxSaveReqVO {

    @Schema(description = "自增长id")
    private Integer id;

    @Schema(description = "主表ID")
    private Integer mainTableId;

    @Schema(description = "送货单号")
    private String lcdh;

    @Schema(description = "采购订单号")
    private String cgddh;

    @Schema(description = "项次")
    private String hh;

    @Schema(description = "料件编号")
    private String wlh;

    @Schema(description = "品名")
    private String wlms;

    @Schema(description = "材质")
    private String cz;

    @Schema(description = "图号")
    private String th;

    @Schema(description = "采购量")
    private BigDecimal cgsl;

    @Schema(description = "原送货数")
    private BigDecimal originalDeliveryQty;

    @Schema(description = "调整后送货数")
    private BigDecimal adjustedDeliveryQty;

    @Schema(description = "调整量")
    private BigDecimal adjustmentQty;

    @Schema(description = "炉号")
    private String lh;

    @Schema(description = "备注")
    private String remark;
}
