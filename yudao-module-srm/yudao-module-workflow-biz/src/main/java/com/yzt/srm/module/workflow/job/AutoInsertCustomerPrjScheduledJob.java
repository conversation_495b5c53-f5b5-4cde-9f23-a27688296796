package com.yzt.srm.module.workflow.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.yzt.module.salesorder.api.CustomerInfoPrjApi;
import com.yzt.module.salesorder.api.dto.CustomerInfoPrjDTO;
import com.yzt.srm.module.zsj.api.tcxmxv.TcXmxVApi;
import com.yzt.srm.module.zsj.api.tcxmxv.dto.TcXmxVDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AutoInsertCustomerPrjScheduledJob implements JobHandler {

    @Resource
    private CustomerInfoPrjApi customerInfoPrjApi;

    @Resource
    private TcXmxVApi tcXmxVApi;

    @Override
    public String execute(String param) throws Exception {

        customerInfoPrjApi.deleteAllData();

        List<TcXmxVDTO> tcXmxVDTOSList = tcXmxVApi.selectAllShiTuDate();

        ArrayList<CustomerInfoPrjDTO> customerPrjDTOS = new ArrayList<>();
        for (TcXmxVDTO tcXmxVDTO : tcXmxVDTOSList){
            CustomerInfoPrjDTO customerPrjDTO = new CustomerInfoPrjDTO();
            customerPrjDTO.setPrjno(tcXmxVDTO.getPrjno());
            customerPrjDTO.setPrjname(tcXmxVDTO.getPrjname());
            customerPrjDTOS.add(customerPrjDTO);
        }

        customerInfoPrjApi.insertBacthData(customerPrjDTOS);

        return "定时任务执行成功";
    }
}
