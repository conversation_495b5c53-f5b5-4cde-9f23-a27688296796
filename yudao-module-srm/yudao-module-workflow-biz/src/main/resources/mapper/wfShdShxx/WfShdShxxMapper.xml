<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yzt.srm.module.workflow.dal.mysql.wfShdShxx.WfShdShxxMapper">

    <select id="selectKjhslByCgddHh"
            parameterType="com.yzt.srm.module.workflow.dal.dataobject.wfShdShxx.WfShdShxxDO"
            resultType="com.yzt.srm.module.workflow.controller.admin.wfShdShxx.vo.ShdSl">
        select a.*
        from srm.cgdd_shsl a
        where cgddh||'-'||hh in
        <foreach collection="shxx" open="(" close=")" separator="," item="item" index="index">
            ${item.cgddh}||'-'||${item.hh}
        </foreach>
    </select>
    <select id="selectWfShdDetailPage" resultType="com.yzt.srm.module.workflow.controller.admin.wfShd.vo.WfShdDetailRespVO">
        SELECT
            shxx.id,
            shd.mc as companyName,
            shd.cgy,
            shd.gysdm,
            shd.gysmc,
            shd.lcdh,
            shxx.cgddh,
            shxx.hh,
            shxx.wlh,
            shxx.mc_lbj,
            shxx.th,
            shxx.sl as cgsl,
            shxx.sjshsl as bcshs,
            shxx.lh,
            shxx.cz
        FROM srm.wf_shd_shxx shxx
        LEFT JOIN srm.wf_shd shd ON shxx.main_table_id = shd.id
        WHERE shxx.invoice_status = 0
            <if test='pageReqVO.gysmc != null and pageReqVO.gysmc != ""'>
               AND shd.gysmc LIKE CONCAT('%', #{pageReqVO.gysmc}, '%')
           </if>
            <if test='pageReqVO.lcdh != null and pageReqVO.lcdh != ""'>
                AND shd.lcdh LIKE CONCAT('%', #{pageReqVO.lcdh}, '%')
           </if>
            <if test='pageReqVO.wlh != null and pageReqVO.wlh != ""'>
               AND shxx.wlh LIKE CONCAT('%', #{pageReqVO.wlh}, '%')
           </if>
            <if test='pageReqVO.lh != null and pageReqVO.lh != ""'>
               AND shxx.lh LIKE CONCAT('%', #{pageReqVO.lh}, '%')
           </if>
        ORDER BY shxx.id DESC
    </select>

    <update id="updateCgddYjhsl" parameterType="String">
        with a as (
            select b.cgddh,b.hh,b.bcshsl
            from srm.wf_shd_shxx b
                     left join srm.wf_shd a on a.id=b.main_table_id
            where a.process_instance_id=#{processInstanceId}
        )
        update srm.zsj_cgdd_wlxx b
        set yjhsl=coalesce(b.yjhsl,0)+a.bcshsl
        from a
        where a.hh=b.hh and b.main_table_id in (select id from srm.zsj_cgdd where cgddh=a.cgddh)
    </update>

</mapper>