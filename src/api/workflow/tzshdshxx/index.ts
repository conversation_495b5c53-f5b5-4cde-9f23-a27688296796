import request from '@/config/axios'

// 送货单调整明细流程 VO
export interface TzShdShxxVO {
  id: number // 自增长id
  mainTableId: number // 送货单id
  cgddh: string // 采购订单号
  shdh: string // 送货单号
  hh: string // 行号
  wlh: string // 物料号
  lh: string // 炉号
  bz: string // 备注
  cz: string // 材质
  th: string // 图号
  sl: number // 数量（采购量）
  mcLbj: string // 名称/零部件
  sjshsl: number // 实际送货数量
  tzhshs: number // 调整后送货数
  tzl: number // 调整量
}

// 送货单调整明细流程 API
export const TzShdShxxApi = {
  // 查询送货单调整明细流程分页
  getTzShdShxxPage: async (params: any) => {
    return await request.get({ url: `/workflow/tz-shd-shxx/page`, params })
  },

  // 查询送货单调整明细流程详情
  getTzShdShxx: async (id: number) => {
    return await request.get({ url: `/workflow/tz-shd-shxx/get?id=` + id })
  },

  // 新增送货单调整明细流程
  createTzShdShxx: async (data: TzShdShxxVO) => {
    return await request.post({ url: `/workflow/tz-shd-shxx/create`, data })
  },

  // 修改送货单调整明细流程
  updateTzShdShxx: async (data: TzShdShxxVO) => {
    return await request.put({ url: `/workflow/tz-shd-shxx/update`, data })
  },

  // 删除送货单调整明细流程
  deleteTzShdShxx: async (id: number) => {
    return await request.delete({ url: `/workflow/tz-shd-shxx/delete?id=` + id })
  },

  // 导出送货单调整明细流程 Excel
  exportTzShdShxx: async (params) => {
    return await request.download({ url: `/workflow/tz-shd-shxx/export-excel`, params })
  },
}
