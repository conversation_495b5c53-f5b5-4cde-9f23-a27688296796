import request from '@/config/axios'

// 送货数调整流程 VO
export interface TzShdVO {
  id: number // 自增长id
  gysmc: string // 供应商名称
  gysdm: string // 供应商代码
  lcdh: string // 流程单号
  cgy: string // 采购员
  cgyName: string // 采购员名称
  mc: string // 公司名称
  dm: string // 公司代码
  tzrq: string // 送货调整日期
  processInstanceId: string // 流程id
  lczt: number // 流程状态
}

// 送货数调整流程 API
export const TzShdApi = {
  // 查询送货数调整流程分页
  getTzShdPage: async (params: any) => {
    return await request.get({ url: `/workflow/tz-shd/page`, params })
  },

  // 查询送货数调整流程详情
  getTzShd: async (id: number) => {
    return await request.get({ url: `/workflow/tz-shd/get?id=` + id })
  },

  // 新增送货数调整流程
  createTzShd: async (data: TzShdVO) => {
    return await request.post({ url: `/workflow/tz-shd/create`, data })
  },

  // 修改送货数调整流程
  updateTzShd: async (data: TzShdVO) => {
    return await request.put({ url: `/workflow/tz-shd/update`, data })
  },

  // 删除送货数调整流程
  deleteTzShd: async (id: number) => {
    return await request.delete({ url: `/workflow/tz-shd/delete?id=` + id })
  },

  // 导出送货数调整流程 Excel
  exportTzShd: async (params) => {
    return await request.download({ url: `/workflow/tz-shd/export-excel`, params })
  },
}
