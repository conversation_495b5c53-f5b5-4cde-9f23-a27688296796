import request from '@/config/axios'

// 销售单别 VO
export interface CategoryVO {
  id: number // 自增长id
  code: string // 销售单别代码
  name: string // 销售单别名称
}

// 销售单别 API
export const CategoryApi = {
  // 查询销售单别分页
  getCategoryPage: async (params: any) => {
    return await request.get({ url: `/salesorder/category/page`, params })
  },

  // 查询销售单别详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/salesorder/category/get?id=` + id })
  },

  // 新增销售单别
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/salesorder/category/create`, data })
  },

  // 修改销售单别
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/salesorder/category/update`, data })
  },

  // 删除销售单别
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/salesorder/category/delete?id=` + id })
  },

  // 导出销售单别 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/salesorder/category/export-excel`, params })
  },
}
