import request from '@/config/axios'

export interface TzShdVO {
  id: number
  lcdh: string
  gysdm: string
  gysmc: string
  cgy: string
  tzrq: Date
  lczt: number
  createTime: Date
}

export interface TzShdShxxVO {
  id: number
  mainTableId: number
  cgddh: string
  shdh: string
  hh: string
  wlh: string
  lh: string
  bz: string
  cz: string
  th: string
  sl: number
  mcLbj: string
  sjshsl: number
  tzhshs: number
  tzl: number
}

export interface TzShdCreateReqVO {
  gysdm: string
  gysmc: string
  cgyName: string
  adjustDetails: TzShdShxxVO[]
}

// 查询送货数调整流程列表
export const getTzShdPage = async (params) => {
  return await request.get({ url: '/workflow/tz-shd/page', params })
}

// 查询送货数调整流程详情
export const getTzShd = async (id: number) => {
  return await request.get({ url: '/workflow/tz-shd/get?id=' + id })
}

// 新增送货数调整流程
export const createTzShd = async (data: TzShdVO) => {
  return await request.post({ url: '/workflow/tz-shd/create', data })
}

// 修改送货数调整流程
export const updateTzShd = async (data: TzShdVO) => {
  return await request.put({ url: '/workflow/tz-shd/update', data })
}

// 删除送货数调整流程
export const deleteTzShd = async (id: number) => {
  return await request.delete({ url: '/workflow/tz-shd/delete?id=' + id })
}

// 导出送货数调整流程 Excel
export const exportTzShdExcel = async (params) => {
  return await request.download({ url: '/workflow/tz-shd/export-excel', params })
}

// 发起送货数调整流程
export const createTzShdProcess = async (data: TzShdCreateReqVO) => {
  return await request.post({ url: '/workflow/tz-shd/create-process', data })
}
