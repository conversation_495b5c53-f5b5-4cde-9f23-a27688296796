import request from '@/config/axios'

export interface WfShdLhVO {
    id: number
    mainTableId: number
    cgddh: string
    hh: string
    wlh: string
    wlms: string
    cz: string
    lh: string
    xlh: string
    sl: number
    zs: number
}

export interface WfShdRtVO {
    id: number
    mainTableId: number
    cgddh: string
    hh: string
    wlh: string
    wlms: string
    czdm: string
    cjlx: number
    rtlx: number
    ph: string
    pc: string
    sl: number
}

export interface WfShdShxxVO {
    id: number
    mainTableId: number
    cgddh: string
    hh: string
    wlh: string
    bcshs: number
    lh: string
    jz: number
    tsyq: string
    bz: string
    wlms: string
    cz: string
    xsddh: string
    xsddhh: string
    lhbs: string
    xlhbs: string
    th: string
    thbb: string
    qsxlh: string
    jzxlh: string
    sjshs: number // 实际收货数量
    mc:string
}

export interface Shd {
    shdzb: WfShdVO
    shxx: WfShdShxxVO[]
    lhxx: WfShdLhVO[]
    rtxx: WfShdRtVO[]
}

export interface WfShdVO {
    id: number
    gysmc: string
    gysdm: string
    lcdh: string
    shrq: Date
    shdw: string
    cgy: number
    gysdz: string
    ckwl: number
    processInstanceId: string
    lczt: number
    cgz: string
}

//更新实际送货数
export const updataSjshs = async (data:any) => {
  return await request.post({url: `/workflow/wf-shd/updataSjshs`,data})
}

export const createShd = async (data: Shd) => {
    return await request.post({url: '/workflow/wf-shd/createShd', data})
}

export const saveShd = async (data: Shd) => {
    return await request.post({url: '/workflow/wf-shd/saveShd', data})
}

export const getSaveShd = async () => {
    return await request.get({url: '/workflow/wf-shd/getSaveShd'})
}

export const getShdInfo = async (id: number) => {
    return await request.get({url: '/workflow/wf-shd/getShdInfo?id=' + id})
}

// 查询送货单列表
export const getWfShdPage = async (params) => {
    return await request.get({url: '/workflow/wf-shd/page', params})
}

// 查询送货单详情
export const getWfShd = async (id: number) => {
    return await request.get({url: '/workflow/wf-shd/get?id=' + id})
}

// 新增送货单
export const createWfShd = async (data: WfShdVO) => {
    return await request.post({url: '/workflow/wf-shd/create', data})
}

// 修改送货单
export const updateWfShd = async (data: WfShdVO) => {
    return await request.put({url: '/workflow/wf-shd/update', data})
}

// 删除送货单
export const deleteWfShd = async (id: number) => {
    return await request.delete({url: '/workflow/wf-shd/delete?id=' + id})
}

// 导出送货单 Excel
export const exportWfShdApi = async (params) => {
    return await request.download({url: '/workflow/wf-shd/export-excel', params})
}

export const getShdPrint = async (data) => {
    return await request.get({url: '/workflow/wf-shd/getShdPrint?lcdh=' + data})
}

export const getLhSl = async (data) => {
    return await request.post({url: '/workflow/wf-shd/getLhSl', data})
}

export const getXlhSl = async (data) => {
    return await request.post({url: '/workflow/wf-shd/getXlhSl', data})
}
