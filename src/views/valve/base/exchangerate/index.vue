<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="从币种" prop="fromCurrency">
        <el-input
          v-model="queryParams.fromCurrency"
          placeholder="请输入从币种"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="到币种" prop="toCurrency">
        <el-input
          v-model="queryParams.toCurrency"
          placeholder="请输入到币种"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="汇率" prop="exchangeRate">
        <el-input
          v-model="queryParams.exchangeRate"
          placeholder="请输入汇率"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="有效期起" prop="validFrom">
        <el-date-picker
          v-model="queryParams.validFrom"
          value-format="YYYY-MM-DD"
          type="date"
          placeholder="选择有效期起"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="有效期止" prop="validTo">
        <el-date-picker
          v-model="queryParams.validTo"
          value-format="YYYY-MM-DD"
          type="date"
          placeholder="选择有效期止"
          clearable
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['base:exchange-rate:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['base:exchange-rate:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="80px" />
      <el-table-column label="从币种" align="center" prop="fromCurrency" />
      <el-table-column label="到币种" align="center" prop="toCurrency" />
      <el-table-column label="汇率" align="center" prop="exchangeRate" />
      <el-table-column
        label="有效期起"
        align="center"
        prop="validFrom"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column
        label="有效期止"
        align="center"
        prop="validTo"
        :formatter="dateFormatter2"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['base:exchange-rate:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['base:exchange-rate:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ExchangeRateForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import {dateFormatter, dateFormatter2} from '@/utils/formatTime'
import download from '@/utils/download'
import { ExchangeRateApi, ExchangeRateVO } from '@/api/valve/base/exchangerate'
import ExchangeRateForm from './ExchangeRateForm.vue'

/** 汇率 列表 */
defineOptions({ name: 'ExchangeRate' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ExchangeRateVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fromCurrency: undefined,
  toCurrency: undefined,
  exchangeRate: undefined,
  validFrom: undefined,
  validFrom: [],
  validTo: undefined,
  validTo: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ExchangeRateApi.getExchangeRatePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ExchangeRateApi.deleteExchangeRate(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ExchangeRateApi.exportExchangeRate(queryParams)
    download.excel(data, '汇率.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>