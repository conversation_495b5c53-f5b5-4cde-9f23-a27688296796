<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="从币种" prop="fromCurrency">
        <el-input v-model="formData.fromCurrency" placeholder="请输入从币种" />
      </el-form-item>
      <el-form-item label="到币种" prop="toCurrency">
        <el-input v-model="formData.toCurrency" placeholder="请输入到币种" />
      </el-form-item>
      <el-form-item label="汇率" prop="exchangeRate" >
        <el-input
          type="number"
          v-model="formData.exchangeRate"
          placeholder="请输入汇率"
          step="0.0001"
        :precision="6"
        :min="0"
        controls-position="right"
        />
      </el-form-item>

      <el-form-item label="有效期起" prop="validFrom">
        <el-date-picker
          v-model="formData.validFrom"
          type="date"
          value-format="x"
          placeholder="选择有效期起"
        />
      </el-form-item>
      <el-form-item label="有效期止" prop="validTo">
        <el-date-picker
          v-model="formData.validTo"
          type="date"
          value-format="x"
          placeholder="选择有效期止"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { ExchangeRateApi, ExchangeRateVO } from '@/api/valve/base/exchangerate'

/** 汇率 表单 */
defineOptions({ name: 'ExchangeRateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  fromCurrency: undefined,
  toCurrency: undefined,
  exchangeRate: undefined,
  validFrom: undefined,
  validTo: undefined,
})
const formRules = reactive({
  fromCurrency: [{ required: true, message: '从币种不能为空', trigger: 'blur' }],
  toCurrency: [{ required: true, message: '到币种不能为空', trigger: 'blur' }],
  exchangeRate: [{ required: true, message: '汇率不能为空', trigger: 'blur' }],
  validFrom: [{ required: true, message: '有效期起不能为空', trigger: 'blur' }],
  validTo: [{ required: true, message: '有效期止不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ExchangeRateApi.getExchangeRate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ExchangeRateVO
    if (formType.value === 'create') {
      await ExchangeRateApi.createExchangeRate(data)
      message.success(t('common.createSuccess'))
    } else {
      await ExchangeRateApi.updateExchangeRate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    fromCurrency: undefined,
    toCurrency: undefined,
    exchangeRate: undefined,
    validFrom: undefined,
    validTo: undefined,
  }
  formRef.value?.resetFields()
}
</script>