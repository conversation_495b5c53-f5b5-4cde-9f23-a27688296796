<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="送货单id" prop="mainTableId">
        <el-input v-model="formData.mainTableId" placeholder="请输入送货单id" />
      </el-form-item>
      <el-form-item label="采购订单号" prop="cgddh">
        <el-input v-model="formData.cgddh" placeholder="请输入采购订单号" />
      </el-form-item>
      <el-form-item label="送货单号" prop="shdh">
        <el-input v-model="formData.shdh" placeholder="请输入送货单号" />
      </el-form-item>
      <el-form-item label="行号" prop="hh">
        <el-input v-model="formData.hh" placeholder="请输入行号" />
      </el-form-item>
      <el-form-item label="物料号" prop="wlh">
        <el-input v-model="formData.wlh" placeholder="请输入物料号" />
      </el-form-item>
      <el-form-item label="炉号" prop="lh">
        <el-input v-model="formData.lh" placeholder="请输入炉号" />
      </el-form-item>
      <el-form-item label="备注" prop="bz">
        <el-input v-model="formData.bz" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="材质" prop="cz">
        <el-input v-model="formData.cz" placeholder="请输入材质" />
      </el-form-item>
      <el-form-item label="图号" prop="th">
        <el-input v-model="formData.th" placeholder="请输入图号" />
      </el-form-item>
      <el-form-item label="数量（采购量）" prop="sl">
        <el-input v-model="formData.sl" placeholder="请输入数量（采购量）" />
      </el-form-item>
      <el-form-item label="名称/零部件" prop="mcLbj">
        <el-input v-model="formData.mcLbj" placeholder="请输入名称/零部件" />
      </el-form-item>
      <el-form-item label="实际送货数量" prop="sjshsl">
        <el-input v-model="formData.sjshsl" placeholder="请输入实际送货数量" />
      </el-form-item>
      <el-form-item label="调整后送货数" prop="tzhshs">
        <el-input v-model="formData.tzhshs" placeholder="请输入调整后送货数" />
      </el-form-item>
      <el-form-item label="调整量" prop="tzl">
        <el-input v-model="formData.tzl" placeholder="请输入调整量" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TzShdShxxApi, TzShdShxxVO } from '@/api/workflow/tzshdshxx'

/** 送货单调整明细流程 表单 */
defineOptions({ name: 'TzShdShxxForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  mainTableId: undefined,
  cgddh: undefined,
  shdh: undefined,
  hh: undefined,
  wlh: undefined,
  lh: undefined,
  bz: undefined,
  cz: undefined,
  th: undefined,
  sl: undefined,
  mcLbj: undefined,
  sjshsl: undefined,
  tzhshs: undefined,
  tzl: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TzShdShxxApi.getTzShdShxx(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TzShdShxxVO
    if (formType.value === 'create') {
      await TzShdShxxApi.createTzShdShxx(data)
      message.success(t('common.createSuccess'))
    } else {
      await TzShdShxxApi.updateTzShdShxx(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    mainTableId: undefined,
    cgddh: undefined,
    shdh: undefined,
    hh: undefined,
    wlh: undefined,
    lh: undefined,
    bz: undefined,
    cz: undefined,
    th: undefined,
    sl: undefined,
    mcLbj: undefined,
    sjshsl: undefined,
    tzhshs: undefined,
    tzl: undefined,
  }
  formRef.value?.resetFields()
}
</script>