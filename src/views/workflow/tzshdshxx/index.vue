<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="送货单id" prop="mainTableId">
        <el-input
          v-model="queryParams.mainTableId"
          placeholder="请输入送货单id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="采购订单号" prop="cgddh">
        <el-input
          v-model="queryParams.cgddh"
          placeholder="请输入采购订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="送货单号" prop="shdh">
        <el-input
          v-model="queryParams.shdh"
          placeholder="请输入送货单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="行号" prop="hh">
        <el-input
          v-model="queryParams.hh"
          placeholder="请输入行号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="物料号" prop="wlh">
        <el-input
          v-model="queryParams.wlh"
          placeholder="请输入物料号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="炉号" prop="lh">
        <el-input
          v-model="queryParams.lh"
          placeholder="请输入炉号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="bz">
        <el-input
          v-model="queryParams.bz"
          placeholder="请输入备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="材质" prop="cz">
        <el-input
          v-model="queryParams.cz"
          placeholder="请输入材质"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="图号" prop="th">
        <el-input
          v-model="queryParams.th"
          placeholder="请输入图号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="数量（采购量）" prop="sl">
        <el-input
          v-model="queryParams.sl"
          placeholder="请输入数量（采购量）"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="名称/零部件" prop="mcLbj">
        <el-input
          v-model="queryParams.mcLbj"
          placeholder="请输入名称/零部件"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="实际送货数量" prop="sjshsl">
        <el-input
          v-model="queryParams.sjshsl"
          placeholder="请输入实际送货数量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="调整后送货数" prop="tzhshs">
        <el-input
          v-model="queryParams.tzhshs"
          placeholder="请输入调整后送货数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="调整量" prop="tzl">
        <el-input
          v-model="queryParams.tzl"
          placeholder="请输入调整量"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['workflow:tz-shd-shxx:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['workflow:tz-shd-shxx:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="自增长id" align="center" prop="id" />
      <el-table-column label="送货单id" align="center" prop="mainTableId" />
      <el-table-column label="采购订单号" align="center" prop="cgddh" />
      <el-table-column label="送货单号" align="center" prop="shdh" />
      <el-table-column label="行号" align="center" prop="hh" />
      <el-table-column label="物料号" align="center" prop="wlh" />
      <el-table-column label="炉号" align="center" prop="lh" />
      <el-table-column label="备注" align="center" prop="bz" />
      <el-table-column label="材质" align="center" prop="cz" />
      <el-table-column label="图号" align="center" prop="th" />
      <el-table-column label="数量（采购量）" align="center" prop="sl" />
      <el-table-column label="名称/零部件" align="center" prop="mcLbj" />
      <el-table-column label="实际送货数量" align="center" prop="sjshsl" />
      <el-table-column label="调整后送货数" align="center" prop="tzhshs" />
      <el-table-column label="调整量" align="center" prop="tzl" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['workflow:tz-shd-shxx:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['workflow:tz-shd-shxx:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TzShdShxxForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { TzShdShxxApi, TzShdShxxVO } from '@/api/workflow/tzshdshxx'
import TzShdShxxForm from './TzShdShxxForm.vue'

/** 送货单调整明细流程 列表 */
defineOptions({ name: 'TzShdShxx' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TzShdShxxVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  mainTableId: undefined,
  cgddh: undefined,
  shdh: undefined,
  hh: undefined,
  wlh: undefined,
  lh: undefined,
  bz: undefined,
  cz: undefined,
  th: undefined,
  sl: undefined,
  mcLbj: undefined,
  sjshsl: undefined,
  tzhshs: undefined,
  tzl: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TzShdShxxApi.getTzShdShxxPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TzShdShxxApi.deleteTzShdShxx(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TzShdShxxApi.exportTzShdShxx(queryParams)
    download.excel(data, '送货单调整明细流程.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>