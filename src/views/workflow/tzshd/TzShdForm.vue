<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="供应商名称" prop="gysmc">
        <el-input v-model="formData.gysmc" placeholder="请输入供应商名称" />
      </el-form-item>
      <el-form-item label="供应商代码" prop="gysdm">
        <el-input v-model="formData.gysdm" placeholder="请输入供应商代码" />
      </el-form-item>
      <el-form-item label="流程单号" prop="lcdh">
        <el-input v-model="formData.lcdh" placeholder="请输入流程单号" />
      </el-form-item>
      <el-form-item label="采购员" prop="cgy">
        <el-input v-model="formData.cgy" placeholder="请输入采购员" />
      </el-form-item>
      <el-form-item label="采购员名称" prop="cgyName">
        <el-input v-model="formData.cgyName" placeholder="请输入采购员名称" />
      </el-form-item>
      <el-form-item label="公司名称" prop="mc">
        <el-input v-model="formData.mc" placeholder="请输入公司名称" />
      </el-form-item>
      <el-form-item label="公司代码" prop="dm">
        <el-input v-model="formData.dm" placeholder="请输入公司代码" />
      </el-form-item>
      <el-form-item label="送货调整日期" prop="tzrq">
        <el-input v-model="formData.tzrq" placeholder="请输入送货调整日期" />
      </el-form-item>
      <el-form-item label="流程id" prop="processInstanceId">
        <el-input v-model="formData.processInstanceId" placeholder="请输入流程id" />
      </el-form-item>
      <el-form-item label="流程状态" prop="lczt">
        <el-input v-model="formData.lczt" placeholder="请输入流程状态" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TzShdApi, TzShdVO } from '@/api/workflow/tzshd'

/** 送货数调整流程 表单 */
defineOptions({ name: 'TzShdForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  gysmc: undefined,
  gysdm: undefined,
  lcdh: undefined,
  cgy: undefined,
  cgyName: undefined,
  mc: undefined,
  dm: undefined,
  tzrq: undefined,
  processInstanceId: undefined,
  lczt: undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TzShdApi.getTzShd(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TzShdVO
    if (formType.value === 'create') {
      await TzShdApi.createTzShd(data)
      message.success(t('common.createSuccess'))
    } else {
      await TzShdApi.updateTzShd(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    gysmc: undefined,
    gysdm: undefined,
    lcdh: undefined,
    cgy: undefined,
    cgyName: undefined,
    mc: undefined,
    dm: undefined,
    tzrq: undefined,
    processInstanceId: undefined,
    lczt: undefined,
  }
  formRef.value?.resetFields()
}
</script>