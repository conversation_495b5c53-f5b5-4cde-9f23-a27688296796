<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="供应商名称" prop="gysmc">
        <el-input
          v-model="queryParams.gysmc"
          placeholder="请输入供应商名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="供应商代码" prop="gysdm">
        <el-input
          v-model="queryParams.gysdm"
          placeholder="请输入供应商代码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程单号" prop="lcdh">
        <el-input
          v-model="queryParams.lcdh"
          placeholder="请输入流程单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="采购员" prop="cgy">
        <el-input
          v-model="queryParams.cgy"
          placeholder="请输入采购员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="采购员名称" prop="cgyName">
        <el-input
          v-model="queryParams.cgyName"
          placeholder="请输入采购员名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="公司名称" prop="mc">
        <el-input
          v-model="queryParams.mc"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="公司代码" prop="dm">
        <el-input
          v-model="queryParams.dm"
          placeholder="请输入公司代码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="送货调整日期" prop="tzrq">
        <el-input
          v-model="queryParams.tzrq"
          placeholder="请输入送货调整日期"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程id" prop="processInstanceId">
        <el-input
          v-model="queryParams.processInstanceId"
          placeholder="请输入流程id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="流程状态" prop="lczt">
        <el-input
          v-model="queryParams.lczt"
          placeholder="请输入流程状态"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['workflow:tz-shd:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['workflow:tz-shd:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="自增长id" align="center" prop="id" />
      <el-table-column label="供应商名称" align="center" prop="gysmc" />
      <el-table-column label="供应商代码" align="center" prop="gysdm" />
      <el-table-column label="流程单号" align="center" prop="lcdh" />
      <el-table-column label="采购员" align="center" prop="cgy" />
      <el-table-column label="采购员名称" align="center" prop="cgyName" />
      <el-table-column label="公司名称" align="center" prop="mc" />
      <el-table-column label="公司代码" align="center" prop="dm" />
      <el-table-column label="送货调整日期" align="center" prop="tzrq" />
      <el-table-column label="流程id" align="center" prop="processInstanceId" />
      <el-table-column label="流程状态" align="center" prop="lczt" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['workflow:tz-shd:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['workflow:tz-shd:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TzShdForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { TzShdApi, TzShdVO } from '@/api/workflow/tzshd'
import TzShdForm from './TzShdForm.vue'

/** 送货数调整流程 列表 */
defineOptions({ name: 'TzShd' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TzShdVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  gysmc: undefined,
  gysdm: undefined,
  lcdh: undefined,
  cgy: undefined,
  cgyName: undefined,
  mc: undefined,
  dm: undefined,
  tzrq: undefined,
  processInstanceId: undefined,
  lczt: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TzShdApi.getTzShdPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TzShdApi.deleteTzShd(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TzShdApi.exportTzShd(queryParams)
    download.excel(data, '送货数调整流程.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>