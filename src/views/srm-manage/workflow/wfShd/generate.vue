<template>
  <ContentWrap>
    <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
    >
      <div class="search-container">
        <div class="search-form-items">
          <el-form-item label="公司名称" prop="companyName" label-width="80px">
            <el-select
                v-model="queryParams.companyName"
                filterable
                remote
                clearable
                placeholder="请选择公司"
                :remote-method="handleCompanySearch"
                :loading="companyLoading"
                style="width: 220px"
            >
              <el-option
                v-for="item in companyOptions"
                :key="item.id"
                :label="item.mc"
                :value="item.mc"
              />
              <el-empty v-if="companyOptions.length === 0" description="暂无公司数据" />
            </el-select>
          </el-form-item>
          <el-form-item label="采购订单号" prop="cgddh" label-width="90px">
            <el-input
                v-model="queryParams.cgddh"
                clearable
                @keyup.enter="handleQuery"
                style="width: 180px"
                placeholder="请输入采购订单号"
            />
          </el-form-item>
          <el-form-item label="品号" prop="ph" label-width="50px">
            <el-input
                v-model="queryParams.ph"
                clearable
                @keyup.enter="handleQuery"
                style="width: 180px"
                placeholder="请输入品号"
            />
          </el-form-item>
          <el-form-item label="品名" prop="mcLbj" label-width="50px">
            <el-input
                v-model="queryParams.mcLbj"
                clearable
                @keyup.enter="handleQuery"
                style="width: 180px"
                placeholder="请输入品名"
            />
          </el-form-item>
          <el-form-item label="图号" prop="th" label-width="50px">
            <el-input
                v-model="queryParams.th"
                clearable
                @keyup.enter="handleQuery"
                style="width: 180px"
                placeholder="请输入图号"
            />
          </el-form-item>
        </div>
        <div class="search-form-btns">
          <el-form-item class="!mb-0">
            <el-button type="primary" @click="handleQuery">
              <Icon icon="ep:search" class="mr-1"/>
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-1"/>
              重置
            </el-button>
            <el-button
                type="primary"
                plain
                @click="generateShd()"
            >
              <Icon icon="ep:plus" class="mr-1"/>
              生成送货单
            </el-button>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-2">
    <el-table v-loading="loading"
              :data="list"
              :stripe="true"
              :show-overflow-tooltip="true"
              @selection-change="handleSelectionChange"
              style="width: 100%"
              ref="tableRef">
      <el-table-column type="selection" width="55"/>
      <el-table-column label="行id" align="center" prop="id" v-if="false"/>
      <el-table-column label="采购订单号" align="center" prop="cgddh" width="200"/>
      <el-table-column label="订单项次" align="center" prop="ddxc" width="100"/>
      <el-table-column label="品号" align="center" prop="ph" width="160"/>
      <el-table-column label="公司名称" align="center" prop="mc" width="200"/>
      <el-table-column label="品名" align="center" prop="mcLbj" width="150"/>
      <el-table-column label="规则型号" align="center" prop="ggxh" width="120"/>
      <el-table-column label="单位" align="center" prop="dw" width="80"/>
      <el-table-column label="图号" align="center" prop="th" width="200" />
      <el-table-column label="数量" align="center" prop="sl" width="90"/>
      <el-table-column label="已送货数量" align="center" prop="yshs" width="110"/>
      <el-table-column label="已入库数量" align="center" prop="yrks" width="110"/>
      <el-table-column label="退货数" align="center" prop="ths" width="90"/>
      <el-table-column label="可交货数量" align="center" prop="kjhs" width="110"/>
      <el-table-column label="交货时间" align="center" prop="jhsj" width="100"/>
      <el-table-column label="回复交期" align="center" prop="hfjq" width="100"/>
      <el-table-column label="特殊要求" align="center" prop="tsyq" width="250"/>
    </el-table>
    <!-- 分页 -->
    <div class="mt-4">
      <Pagination
          :total="total"
          v-model:page="queryParams.pageNo"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
      />
    </div>
  </ContentWrap>
</template>
<script setup lang="ts">
import {CgddWlxxApi} from "@/api/srm-manage/zsj/cgddwlxx";
import {XfxxApi} from "@/api/srm-manage/basedata/xfxx";
import { onMounted, ref, reactive } from 'vue'

defineOptions({
  name: "GenerateShd",
})
const {push} = useRouter() // 路由
const message = useMessage() // 消息弹窗

const tableRef = ref()

const wlxx = ref([])
const handleSelectionChange = (val) => {
  wlxx.value = val
}

const generateShd = () => {
  if (wlxx.value.length === 0) {
    message.error("请至少选择一条记录！")
    return
  }
  
  // 检查是否存在采购订单号为空的数据
  const emptyOrderItems = wlxx.value.filter(item => !item.cgddh || item.cgddh.trim() === '')
  if (emptyOrderItems.length > 0) {
    message.error("存在采购订单号为空的数据，无法生成送货单！")
    return
  }
  
  // 检查是否所有选中的数据都来自同一家公司
  const companies = new Set(wlxx.value.map(item => item.mc))
  if (companies.size > 1) {
    message.error("不能选择不同公司的数据，请重新选择同一家公司的数据！")
    return
  }
  
  push({
    name: 'CreateShd',
    state: {
      data: JSON.stringify(wlxx.value),
    },
  })
}

const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  mainTableId: undefined,
  hh: undefined,
  ph: undefined,
  cgddh: undefined,
  mcLbj: undefined,
  th: undefined,
  wlms: undefined,
  sl: undefined,
  jhq: undefined,
  dj: undefined,
  thbb: undefined,
  createTime: [],
  tsyq: undefined,
  companyName: undefined, // 新增公司名称查询参数
})

// 公司相关数据
const companyLoading = ref(false)
const companyOptions = ref([])

// 处理公司名称远程搜索
const handleCompanySearch = async (query) => {
  if (query !== undefined) {
    companyLoading.value = true
    try {
      const data = await XfxxApi.getEnableList(query)
      companyOptions.value = data
    } catch (error) {
      console.error("获取公司列表失败", error)
      companyOptions.value = []
    } finally {
      companyLoading.value = false
    }
  }
}

// 初始化时加载公司列表数据
const loadInitialCompanyOptions = async () => {
  companyLoading.value = true
  try {
    const res = await XfxxApi.getEnableList('')
    if (res && res.data) {
      companyOptions.value = res.data
    } else {
      companyOptions.value = []
    }
  } catch (error) {
    console.error("初始化公司列表失败", error)
  } finally {
    companyLoading.value = false
  }
}

const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CgddWlxxApi.getCgddAndWlxxPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 初始化 **/
onMounted(() => {
  loadInitialCompanyOptions()
  getList()
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 12px;
}

.search-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
}

.search-form-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.search-form-btns {
  display: flex;
  align-items: center;
  margin-left: 8px;
  margin-top: 1px;
  margin-bottom: 15px;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
  }
  
  .search-form-btns {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
