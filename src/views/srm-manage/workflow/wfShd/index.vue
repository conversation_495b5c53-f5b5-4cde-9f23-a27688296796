<template>
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <div class="search-container">
        <div class="search-form-items">
          <el-form-item label="供应商名称" prop="gysmc" label-width="100px">
            <el-input
              v-model="queryParams.gysmc"
              clearable
              @keyup.enter="handleQuery"
              style="width: 180px"
              placeholder="请输入供应商名称"
            />
          </el-form-item>
          <el-form-item label="送货单号" prop="lcdh" label-width="100px">
            <el-input
              v-model="queryParams.lcdh"
              clearable
              @keyup.enter="handleQuery"
              style="width: 180px"
              placeholder="请输入送货单号"
            />
          </el-form-item>
          <el-form-item label="品号" prop="wlh" label-width="80px">
            <el-input
              v-model="queryParams.wlh"
              clearable
              @keyup.enter="handleQuery"
              style="width: 180px"
              placeholder="请输入品号"
            />
          </el-form-item>
          <el-form-item label="炉号" prop="lh" label-width="80px">
            <el-input
              v-model="queryParams.lh"
              clearable
              @keyup.enter="handleQuery"
              style="width: 180px"
              placeholder="请输入炉号"
            />
          </el-form-item>
        </div>
        <div class="search-form-btns">
          <el-form-item class="!mb-0">
            <el-button type="primary" @click="handleQuery">
              <Icon icon="ep:search" class="mr-5px"/>
              搜索
            </el-button>
            <el-button @click="resetQuery">
              <Icon icon="ep:refresh" class="mr-5px"/>
              重置
            </el-button>
            <el-button
              plain
              type="primary"
              @click="handleAdjustDelivery"
              :disabled="selectedRows.length === 0"
            >
              <Icon icon="ep:plus" class="mr-1"/>
              发起送货数调整
            </el-button>
          </el-form-item>
        </div>

      </div>
    </el-form>
  </ContentWrap>

  <ContentWrap>
    <el-table v-loading="loading"
              :data="list"
              :stripe="true"
              ref="tableRef"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"/>
      <el-table-column label="公司" align="center" prop="companyName" width="150"/>
      <el-table-column label="采购员" align="center" prop="cgyName" width="100"/>
      <el-table-column label="供应商代码" align="center" prop="gysdm" width="180"/>
      <el-table-column label="供应商名称" align="center" prop="gysmc" width="200"/>
      <el-table-column label="送货单号" align="center" prop="lcdh" width="180"/>
      <el-table-column label="采购订单号" align="center" prop="cgddh" width="200"/>
      <el-table-column label="项次" align="center" prop="hh" width="80"/>
      <el-table-column label="料件编号" align="center" prop="wlh" width="150"/>
      <el-table-column label="品名" align="center" prop="wlms" width="150"/>
      <el-table-column label="图号" align="center" prop="th" width="150"/>
      <el-table-column label="采购量" align="center" prop="cgsl" width="100"/>
      <el-table-column label="送货数" align="center" prop="bcshs" width="100"/>
      <el-table-column label="炉号" align="center" prop="lh" width="120"/>
      <el-table-column label="材质" align="center" prop="cz" width="120"/>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script setup lang="ts" name="WfShd">
import { onMounted, ref, reactive } from 'vue'
import {getWfShdDetailPage} from "@/api/srm-manage/workflow/wfShd";

const { push, currentRoute } = useRouter() // 路由
const message = useMessage() // 消息弹窗
const tableRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据
const total = ref(0) // 列表的总页数
const selectedRows = ref([]) // 选中的行数据

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  gysmc: undefined, // 供应商名称
  lcdh: undefined,  // 送货单号
  wlh: undefined,   // 品号
  lh: undefined     // 炉号
})

const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await getWfShdDetailPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

const detail = (id: number) => {
  push({
    path: '/srm-manage/workflow/shd/detail',
    query: {
      id: id
    }
  })
}

/** 表格选择变化 */
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

/** 发起送货数调整 */
const handleAdjustDelivery = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }

  // 检查是否为同一家供应商且采购员一致
  const firstRow = selectedRows.value[0]
  const gysdm = firstRow.gysdm
  const cgy = firstRow.cgy

  const isValid = selectedRows.value.every(row =>
    row.gysdm === gysdm && row.cgy === cgy
  )

  if (!isValid) {
    message.warning('只允许选择同一家供应商且采购员一致的数据')
    return
  }

  // 跳转到送货数调整页面
  push({
    name: 'ShdTzForm',
    state: {
      data: JSON.stringify(selectedRows.value)
    }
  })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
}

.search-form-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.search-form-btns {
  display: flex;
  align-items: center;
  margin-left: 8px;
  margin-top: 1px;
  margin-bottom: 15px;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
  }

  .search-form-btns {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
