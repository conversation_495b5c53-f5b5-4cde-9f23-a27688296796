<template>
  <el-tabs type="card" style="margin-top: -50px">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="logo-container">
            <img src="@/assets/imgs/logo.png" alt="公司Logo" class="company-logo" />
            <span style="margin-top: 10px">送货数调整流程</span>
          </div>
          <div class="title-container">
            <div class="page-title">送货数调整</div>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              <Icon icon="ep:check" class="mr-5px"/>
              提交
            </el-button>
            <el-button @click="handleCancel">
              <Icon icon="ep:close" class="mr-5px"/>
              取消
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 供应商信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card" style="margin-bottom: 20px">
            <div class="custom-title">供应商信息</div>
            <el-form-item label="供应商代码" prop="gysdm">
              <el-input v-model="formData.gysdm" disabled style="width: 300px"/>
            </el-form-item>
            <el-form-item label="供应商名称" prop="gysmc">
              <el-input v-model="formData.gysmc" disabled style="width: 300px"/>
            </el-form-item>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card" style="margin-bottom: 20px">
            <div class="custom-title">采购员信息</div>
            <el-form-item label="采购员" prop="cgyName">
              <el-input v-model="formData.cgyName" disabled style="width: 300px"/>
            </el-form-item>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>送货数调整明细</span>
        </div>
      </template>
      
      <el-table :data="adjustData" border style="width: 100%" max-height="600px">
        <el-table-column label="序号" type="index" align="center" width="60" fixed>
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="送货单号" align="center" prop="lcdh" width="180" fixed/>
        <el-table-column label="采购订单号" align="center" prop="cgddh" width="180"/>
        <el-table-column label="项次" align="center" prop="hh" width="80"/>
        <el-table-column label="料件编号" align="center" prop="wlh" width="150"/>
        <el-table-column label="品名" align="center" prop="wlms" width="150"/>
        <el-table-column label="材质" align="center" prop="cz" width="120"/>
        <el-table-column label="图号" align="center" prop="th" width="150"/>
        <el-table-column label="采购量" align="center" width="100"></el-table-column>
        <el-table-column label="送货数" align="center" width="100"></el-table-column>
        <el-table-column label="调整后送货数" align="center" width="150">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.adjustedDeliveryQty"
              :min="0"
              :max="scope.row.bcshs"
              :precision="2"
              :step="1"
              :controls="true"
              size="small"
              @change="calculateAdjustment(scope.row)"
              style="width: 120px"
            />
          </template>
        </el-table-column>
        <el-table-column label="调整量" align="center" width="100">
          <template #default="scope">
            <span :class="scope.row.adjustmentQty < 0 ? 'negative-qty' : ''">
              {{ formatNumber(scope.row.adjustmentQty || 0) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="炉号" align="center" prop="lh" width="120"/>
        <el-table-column label="备注" align="center" width="200">
          <template #default="scope">
            <el-input
              v-model="scope.row.remark"
              placeholder="请输入备注"
              size="small"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </el-tabs>
</template>

<script setup lang="ts" name="ShdTzForm">
import { onMounted, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import * as TzShdApi from '@/api/srm-manage/workflow/tzShd'

defineOptions({
  name: 'ShdTzForm'
})

const { push, currentRoute } = useRouter()
const route = useRoute()

const submitLoading = ref(false)
const adjustData = ref([])
const formData = reactive({
  gysdm: '',
  gysmc: '',
  cgyName: ''
})

/** 计算调整量 */
const calculateAdjustment = (row: any) => {
  if (row.adjustedDeliveryQty !== undefined && row.adjustedDeliveryQty !== null) {
    // 确保计算结果保持2位小数
    row.adjustmentQty = parseFloat((row.adjustedDeliveryQty - (row.bcshs || 0)).toFixed(2))
  } else {
    row.adjustmentQty = 0
  }
}

/** 格式化数字显示2位小数 */
const formatNumber = (value: number) => {
  if (value === null || value === undefined) return '0.00'
  return parseFloat(value.toString()).toFixed(2)
}

/** 提交调整 */
const handleSubmit = async () => {
  // 验证调整后送货数
  const hasInvalidData = adjustData.value.some(row => {
    return row.adjustedDeliveryQty === undefined || 
           row.adjustedDeliveryQty === null || 
           row.adjustedDeliveryQty > row.bcshs
  })

  if (hasInvalidData) {
    ElMessage.warning('调整后送货数不能为空且不能大于原送货数')
    return
  }

  // 检查是否有调整量为0的数据
  const hasNoAdjustment = adjustData.value.every(row => row.adjustmentQty === 0)
  if (hasNoAdjustment) {
    ElMessage.warning('至少需要调整一条数据')
    return
  }

  submitLoading.value = true
  try {
    // 构造提交数据
    const submitData = {
      gysdm: formData.gysdm,
      gysmc: formData.gysmc,
      cgyName: formData.cgyName,
      adjustDetails: adjustData.value.map(row => ({
        lcdh: row.lcdh,
        cgddh: row.cgddh,
        hh: row.hh,
        wlh: row.wlh,
        wlms: row.wlms,
        cz: row.cz,
        th: row.th,
        cgsl: row.cgsl,
        originalDeliveryQty: row.bcshs,
        adjustedDeliveryQty: row.adjustedDeliveryQty,
        adjustmentQty: row.adjustmentQty,
        lh: row.lh,
        remark: row.remark || ''
      }))
    }

    // 调用后端接口提交数据
    await TzShdApi.createTzShdProcess(submitData)

    ElMessage.success('送货数调整流程发起成功')
    handleCancel()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitLoading.value = false
  }
}

/** 取消操作 */
const handleCancel = () => {
  push('/srm-manage/workflow/wfShd')
}

/** 初始化数据 */
onMounted(async () => {
  const data  = history.state.data as unknown as string
  console.log("data",data)
  const selectedDataStr = JSON.parse(data)
  if (selectedDataStr) {
    try {
      if (selectedDataStr && selectedDataStr.length > 0) {
        // 设置表单数据
        const firstRow = selectedDataStr[0]
        formData.gysdm = firstRow.gysdm
        formData.gysmc = firstRow.gysmc
        formData.cgyName = firstRow.cgyName

        // 设置调整数据
        adjustData.value = selectedDataStr.map(row => ({
          ...row,
          // 确保数值类型正确并保持2位小数
          bcshs: parseFloat((row.bcshs || 0).toString()),
          cgsl: parseFloat((row.cgsl || 0).toString()),
          adjustedDeliveryQty: parseFloat((row.bcshs || 0).toString()), // 默认为原送货数
          adjustmentQty: 0.00,
          remark: ''
        }))
      }
    } catch (error) {
      console.error('解析选中数据失败:', error)
      ElMessage.error('数据解析失败')
      handleCancel()
    }
  } else {
    ElMessage.error('缺少必要数据')
    handleCancel()
  }
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.company-logo {
  height: 40px;
  width: auto;
}

.title-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.custom-title {
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  border-left: 4px solid #409eff;
  padding-left: 10px;
}

.box-card {
  border-radius: 8px;
}

.negative-qty {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}
</style>
