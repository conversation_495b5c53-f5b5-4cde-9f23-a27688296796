import type {CrudSchema} from '@/hooks/web/useCrudSchemas'
import {dateFormatter} from '@/utils/formatTime'
import {getSimpleUserList} from "@/api/system/user";

// 表单校验
export const rules = reactive({})

const userList = await getSimpleUserList()

// CrudSchema https://doc.iocoder.cn/vue3/crud-schema/
const crudSchemas = reactive<CrudSchema[]>([
    {
        label: '自增长id',
        field: 'id',
        dictClass: 'number',
        isForm: false,
        isDescrList: false,
        isTable: false
    },
    {
        label: '流程单号',
        field: 'lcdh',
        dictClass: 'string',
        isSearch: true,
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    {
        label: '送货日期',
        field: 'shrq',
        isSearch: false,
        descrList: {
            component: 'DatePicker',
            componentProps: {
                disabled: true
            }
        },
        search: {
            component: 'DatePicker',
            componentProps: {
                valueFormat: 'YYYY-MM-DD',
                type: 'daterange',
                defaultTime: [new Date('1'), new Date('1')]
            }
        },
        form: {
            component: 'DatePicker',
            componentProps: {
                type: 'date',
                valueFormat: 'YYYY-MM-DD'
            }
        }
    },
    {
        label: '供应商代码',
        field: 'gysdm',
        dictClass: 'string',
        isSearch: false,
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    {
        label: '供应商名称',
        field: 'gysmc',
        dictClass: 'number',
        isSearch: false,
        form: {
            component: 'InputNumber',
            value: 0
        },
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    {
        label: '采购组',
        field: 'cgz',
        dictClass: 'string',
        isSearch: false,
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    {
        label: '收货单位',
        field: 'shdw',
        dictClass: 'string',
        isSearch: false,
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    {
        label: '采购员',
        field: 'cgy',
        dictClass: 'number',
        isSearch: false,
        form: {
            component: 'InputNumber',
            value: 0
        },
        isDescrList: false,
        descrList: {
            component: 'Select',
            api: (row) => userList.filter(item => item.id === row.cgy)[0]?.nickname,
            componentProps: {
                optionsAlias: {
                    labelField: 'nickname',
                    valueField: 'id'
                },
                disabled: true
            }
        }
    },
    {
        label: '供应商地址',
        field: 'gysdz',
        dictClass: 'string',
        isSearch: false,
        isDescrList: false,
        descrList: {
            componentProps: {
                disabled: true
            }
        }
    },
    // {
    //   label: '仓库物流',
    //   field: 'ckwl',
    //   dictClass: 'number',
    //   isSearch: false,
    //   form: {
    //     component: 'InputNumber',
    //     value: 0
    //   },
    //   descrList: {
    //     component: 'Select',
    //     api: () => userList,
    //     componentProps: {
    //       optionsAlias: {
    //         labelField: 'nickname',
    //         valueField: 'id'
    //       },
    //       clearable: false
    //     }
    //   }
    // },
    {
        label: '创建日期',
        field: 'createTime',
        formatter: dateFormatter,
        isSearch: false,
        search: {
            component: 'DatePicker',
            componentProps: {
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                type: 'daterange',
                defaultTime: [new Date('1 00:00:00'), new Date('1 23:59:59')]
            }
        },
        isForm: false,
        isDescrList: false
    },
    {
        label: '流程id',
        field: 'processInstanceId',
        dictClass: 'string',
        isSearch: false,
        isDescrList: false
    },
    {
        label: '流程状态',
        field: 'lczt',
        dictClass: 'number',
        isSearch: false,
        form: {
            component: 'InputNumber',
            value: 0
        },
        isDescrList: false
    },
    {
        label: '操作',
        field: 'action',
        isForm: false,
        isDescrList: false
    }
])
export const {allSchemas} = useCrudSchemas(crudSchemas)
