<template>
  <ContentWrap>
    <SearchForm
      ref="searchFormRef"
      :schema="schema"
      :model="queryParams"
      :api="getList"
      auto-search
      @reset="handleReset"
    >
      <template #actionButtons>
        <!-- 使用新的按钮组组件 -->
        <ActionButtonGroup 
          :buttons="actionButtons" 
          @action="handleAction"
        />
      </template>
    </SearchForm>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="序号" align="center" type="index" width="80px" />
      <el-table-column label="请购单类型" align="center" prop="qgdlx">
        <template #default="scope">
          <dict-tag :type="'srm_qgdlb'" :value="scope.row.qgdlx" />
        </template>
      </el-table-column>
      <el-table-column label="材质" align="center" prop="cz" />
      <el-table-column label="编号" align="center" prop="bh" />
      <el-table-column label="名称" align="center" prop="mc" width="500" />
      <el-table-column label="操作" align="center" width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['qa:cggf:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['qa:cggf:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CggfForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CggfApi, CggfVO } from '@/api/srm-manage/qa/cggf'
import CggfForm from './CggfForm.vue'
import { reactive, ref } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { useI18n } from 'vue-i18n'

defineOptions({ name: 'Cggf' })

const message = useMessage()
const { t } = useI18n()

const loading = ref(true)
const list = ref<CggfVO[]>([])
const total = ref(0)

// 初始查询参数
const queryParams = {
  pageNo: 1,
  pageSize: 10,
  qgdlx: undefined,
  cz: undefined,
  bh: undefined,
  mc: undefined
}

const exportLoading = ref(false)

const getList = async (searchParams = {}) => {
  Object.assign(queryParams, searchParams)
  queryParams.pageNo = 1
  loading.value = true
  try {
    const data = await CggfApi.getCggfPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const schema = ref([
  {
    field: 'qgdlx',
    label: '请购单类型',
    component: 'Select',
    componentProps: {
      options: getIntDictOptions(DICT_TYPE.CG_QGDLB)
    }
  },
  { field: 'cz', label: '材质', component: 'Input' },
  { field: 'bh', label: '编号', component: 'Input' },
  { field: 'mc', label: '名称', component: 'Input' }
])


// 定义操作按钮
const actionButtons = [
  {
    key: 'create',
    text: '新增',
    type: 'primary',
    icon: 'ep:plus',
    plain: true,
    permission: ['qa:cggf:create'],
    handler: () => openForm('create')
  },
  {
    key: 'export',
    text: '导出',
    type: 'success',
    icon: 'ep:download',
    plain: true,
    permission: ['qa:cggf:export'],
    disabled: exportLoading.value,
    handler: () => handleExport()
  }
]

// 处理按钮点击事件
const handleAction = (key: string) => {
  console.log('Button clicked:', key)
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await CggfApi.deleteCggf(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    await message.exportConfirm()
    exportLoading.value = true
    const data = await CggfApi.exportCggf(queryParams)
    download.excel(data, '采购规范.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// ✅ 处理重置事件
const handleReset = (values: any) => {
  // 重置分页参数
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  // 合并重置后的 values
  Object.assign(queryParams, values)
  // 重新获取列表
  getList()
}
</script>