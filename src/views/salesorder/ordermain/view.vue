<template>

  <!-- 浮动打印按钮 -->
  <div class="print-button-float">
    <el-button
      @click="print()"
      type="primary"
      :disabled="formLoading"
    >打印</el-button>
  </div>

  <div class="full-width-container">
    <div class="scroll-content">
      <el-tabs tab-position="left" @tab-click="handleTabClick" >
        <el-tab-pane label="订单信息">
            <el-form
              ref="formRef"
              :model="formData"
              label-width="138px"
              v-loading="formLoading"
            >
                  <el-row>
                    <el-col
                      colspan="2"
                      style="text-align: center; font-weight: bold; font-size: 24px; padding: 10px"
                      >合同评审</el-col
                    >
                  </el-row>
                  <!-- <el-row>
                    <el-col colspan="2" :span="20">
                      <el-form-item label="评审单号" prop="requestNo" >
                        <span>{{ formData.requestno }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row> -->
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="提交人" prop="submitter">
                        <span>{{ submitterName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="提交日期" prop="submitdate">
                        <span>{{ formData.submitdate }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-collapse v-model="activeNames" style="margin-left: 5%;margin-right: 5%;">
                      <el-collapse-item title="一.合同基本信息" name="1">
                        <el-row>
                          <el-col :span="12">
                              <el-form-item label="销售订单号" prop="salesorder">
                                <el-input v-model="formData.salesorder" disabled/>
                              </el-form-item>
                          </el-col>
      
                          <el-col :span="12">
                            <el-form-item label="参考销售订单号" prop="reforder">
                              <el-input v-model="formData.reforder" disabled/>
                            </el-form-item>
                              <!-- <el-form-item label="报价单号" prop="quoteno">
                                <el-input v-model="formData.quoteno" disabled>
                                </el-input>
                              </el-form-item> -->
                          </el-col>
      
                        </el-row>
                        
                        <el-row>
                          <el-col :span="12">
                              <el-form-item label="项目编号" prop="prjno">
                                <el-input v-model="formData.prjno" :disabled="true">
                                  
                                </el-input>
                              </el-form-item>
                          </el-col>
      
                          <el-col :span="12">
                              <el-form-item label="项目名称" prop="prjname">
                                <el-input v-model="formData.prjname" disabled />
                              </el-form-item>
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="12">            
                              <el-form-item label="客户代码" prop="customercode">
                                <el-input v-model="formData.customercode" :disabled="true">
                                
                                </el-input>
                              </el-form-item>
                          </el-col>
      
                          <el-col :span="12">
                            <el-form-item label="客户名称" prop="customername">
                              <el-input v-model="formData.customername" disabled/>
                            </el-form-item>
                          </el-col>                                  
                        </el-row>
      
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="客户订单号" prop="ctmorderno">
                              <el-input v-model="formData.ctmorderno" disabled/>
                            </el-form-item>
                          </el-col>     
      
      
                          <el-col :span="12">
                              <el-form-item label="付款方式" prop="payway">
                                <el-input v-model="formData.payway" disabled/>
                              </el-form-item>
                            </el-col>          
                        </el-row>     
                            
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="客户下单确认日期" prop="podate">
                              <el-date-picker
                                v-model="formData.podate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                disabled
                              />
                            </el-form-item>
                          </el-col>
      
                          <el-col :span="12">
                            <el-form-item label="请求交货期" prop="delydate">
                              <el-date-picker
                                key="hellodely"
                                v-model="formData.delydate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                disabled
                              />
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                        
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="币种" prop="currency">
                              <el-select v-model="formData.currency" disabled>
                                <el-option
                                  v-for="(dict, $index) in getStrDictOptions(DICT_TYPE.SALESORDER_BZ)"
                                  :key="$index"
                                  :label="dict.label"
                                  :value="dict.value"
                                  
                                />
                              </el-select>
                            </el-form-item>          
                          </el-col>
      
                          <el-col :span="12">
                              <el-form-item label="订单金额" prop="orderamt" >
                                    <el-input-number :precision="2" v-model="formData.orderamt" disabled/>
                              </el-form-item>
                            
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="协调评审" prop="teamwork">
                              <el-select
                                v-model="formData.teamwork"
                                filterable
                                :filter-method="filterOptions"
                                clearable
                                multiple
                                style="width: 100%"
                                disabled
                              >
                                <el-option
                                  v-for="item in filteredOptions"
                                  :key="item.id"
                                  :label="item.nickname"
                                  :value="item.id"
                                />
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                                
                      </el-collapse-item>
      
      
                      <el-collapse-item title=" 二.合同附件" name="2">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="技术条款附件" prop="techfiles" >
                              <UploadFile v-model="formData.techfiles" disabled/>
                            </el-form-item>
                          </el-col>
      
                          <el-col :span="12">
                            <el-form-item label="合同附件" prop="orderfiles">
                              <UploadFile v-model="formData.orderfiles" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-collapse-item>
      
      
                      <el-collapse-item title=" 三、合同要求" name="3">
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="技术、质量要求"   prop="techremark">
                              <el-input :rows="5" type="textarea" v-model="formData.techremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="检验、验收要求"   prop="inspecremark">
                              <el-input :rows="5" type="textarea" v-model="formData.inspecremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="标志商标"   prop="markremark">
                              <el-input :rows="5" type="textarea" v-model="formData.markremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="油漆要求、包装要求"   prop="paintremark">
                              <el-input :rows="5" type="textarea" v-model="formData.paintremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="文件要求"   prop="docremark">
                              <el-input :rows="5" type="textarea" v-model="formData.docremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="特殊要求"   prop="specialremark">
                              <el-input :rows="5" type="textarea" v-model="formData.specialremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
      
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="其他约定"   prop="otherremark">
                              <el-input :rows="5" type="textarea" v-model="formData.otherremark" resize="none" disabled/>
                            </el-form-item>
                          </el-col>
                        </el-row>
      
                      </el-collapse-item>
      
                      <!-- <el-collapse-item title=" 四，销售订单号" name="4">
                        <el-row>
                          <el-col :span="12">
                              
                          </el-col>                
                        </el-row>
                      </el-collapse-item> -->
                  </el-collapse>
            </el-form>
        </el-tab-pane>
      
        <el-tab-pane label="特殊要求" >
          <detailVue ref="detailRef" :id="id" v-if="detailShow"/>
        </el-tab-pane>
      
        <el-tab-pane label="汇总">
          <summaryVue :id="id" v-if="summaryShow" />
        </el-tab-pane>

        <el-tab-pane label="流程记录">
          <processInstanceVue :id="id" v-if="processInstanceShow" />
        </el-tab-pane>
      
        </el-tabs>
    </div>
  </div>
  
 

 </template>
 
 
 <style scoped>
/* 浮动打印按钮 - 圆形设计 */
.print-button-float {
  position: fixed;
  top: 20px;
  right: 35px;
  z-index: 1000;
  width: 40px;
  height: 40px;
  border-radius: 50%; /* 圆形 */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #409eff; /* Element Plus 主题色 */
}

.print-button-float:hover {
  background-color: #66b1ff; /* 悬停效果 */
  transform: scale(1.05); /* 轻微放大效果 */
  transition: all 0.3s ease;
}

.print-button-float:active {
  transform: scale(0.95); /* 点击效果 */
}

/* 如果需要调整按钮内文字或图标 */
.print-button-float :deep(.el-button) {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  padding: 0;
  border: none;
  background-color: transparent;
  color: white;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 全宽容器 */
.full-width-container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  padding-top: 5px; /* 为浮动按钮留出一些空间 */
}

/* 可滚动内容区域 */
.scroll-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
}

.scroll-content::-webkit-scrollbar {
  width: 6px;
}

/* 表单标题样式 */
.form-title {
  text-align: center; 
  font-weight: bold; 
  font-size: 24px; 
  padding: 10px;
}

/* 表单折叠面板样式 */
.form-collapse {
  margin: 0 5%;
}

/* 其他原有样式 */
.item .el-form-item__label {
  color: red;
}

.el-tabs__item {
  padding: 0 10px;
}
</style>
 <script setup lang="ts">
 import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
 import { OrderMainApi } from '@/api/salesorder/ordermain'
 import detailVue from './detail.vue'
 import summaryVue from './summary.vue'
import processInstanceVue from './processInstance.vue'
 import { number } from 'vue-types'
 import * as UserApi from '@/api/system/user'
 import * as DeptApi from '@/api/system/dept'
 import { Base64 } from 'js-base64'
 
 const filteredOptions=ref<UserApi.UserVO[]>([])
 const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
 const activeNames = ref(['1','2','3','4'])
 const submitterName=ref('') // 提交人
 
 /** 合同评审流程 表单 */
 defineOptions({ name: 'orderMainView' })
  
 const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
 const formData = ref({
   id: undefined,
   processInstanceId: undefined,
   requestno: undefined,
   submitter: undefined,
   department: undefined,
   submitdate: undefined,
   reforder: undefined,
   customername: undefined,
   customercode: undefined,
   ctmorderno: undefined,
   quoteno: undefined,
   prjno: undefined,
   prjname: undefined,
   podate: undefined,
   delydate: undefined,
   currency: undefined,
   orderamt: undefined,
   payway: undefined,
   techfiles: undefined,
   orderfiles: undefined,
   techremark: undefined,
   inspecremark: undefined,
   markremark: undefined,
   paintremark: undefined,
   docremark: undefined,
   specialremark: undefined,
   otherremark: undefined,
   salesorder: undefined,
   teamwork:undefined,
   mid: undefined,
   modifytype:undefined
 })
 const formRef = ref() // 表单 Ref
  
//  const props = defineProps({
//    id: {
//      type: number,
//      // required: true
//    }
//  })
 
 
 /** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    processInstanceId: undefined,
    requestno: undefined,
    submitter: undefined,
    department: undefined,
    submitdate: undefined,
    reforder: undefined,
    customername: undefined,
    customercode: undefined,
    ctmorderno: undefined,
    quoteno: undefined,
    prjno: undefined,
    prjname: undefined,
    podate: undefined,
    delydate: undefined,
    currency: undefined,
    orderamt: undefined,
    payway: undefined,
    techfiles: undefined,
    orderfiles: undefined,
    techremark: undefined,
    inspecremark: undefined,
    markremark: undefined,
    paintremark: undefined,
    docremark: undefined,
    specialremark: undefined,
    otherremark: undefined,
    salesorder: undefined,
    teamwork:undefined,
    mid: undefined,
    modifytype: undefined
  }
  formRef.value?.resetFields()
}

const id=ref()
const router = useRouter()


onMounted(async () => { 
  resetForm()
  const eid=router.currentRoute.value.query.id
  if (eid) {
  const mid=Base64.decode(eid)
  if (!isNaN(mid) && Number.isInteger(Number(mid))) {
    id.value=mid
    formLoading.value = true
    try {
      formData.value =await OrderMainApi.getOrderMain(mid)
        
      const userInfo=await UserApi.getUser(formData.value.submitter)
      submitterName.value=userInfo.nickname

      if(formData.value.department!=null)
      {
        const deptInfo=await DeptApi.getDept(formData.value.department)
        submitterName.value=userInfo.nickname+'  '+deptInfo.name
      }

      if(formData.value.teamwork!=null)
      {
        var teamwork=formData.value.teamwork.split(",")
        for(var i=0;i<teamwork.length;i++)
        {
            const userInfo=await UserApi.getUser(teamwork[i])
            filteredOptions.value.push(userInfo)
        }
      }

    } finally {
      formLoading.value = false
    }
  }
  else
  {
    ElMessage.error('参数错误')
  }
    
  }
})
 
 
//  watch(
//    () => props.id,
//    () => (
//      open()
   
//    ),
//    { immediate: true }
//  )
 

 
 const filterOptions=(query)=>{
   if (!query) {
       filteredOptions.value = userOptions.value;
       return;
   }
   filteredOptions.value = userOptions.value.filter(item =>
     item.nickname.toLowerCase().includes(query.toLowerCase())
   );
 }
 
 const detailShow=ref(false)
 const summaryShow=ref(false)
 const processInstanceShow=ref(false)
 
 
 import { ElMessage, type TabsPaneContext } from 'element-plus'
 const handleTabClick = (tab: TabsPaneContext, event: Event) => {
   summaryShow.value = false
   if(tab.props?.label=='特殊要求')
   {
     detailShow.value = true
   }
   else if(tab.props?.label=='汇总')
   {
     summaryShow.value = true
   }
   else if(tab.props?.label=='流程记录')
   {
     processInstanceShow.value = true
   }
 }
 
 
 
 const { resolve } = useRouter()
 const print =()=>{ 
  const url = resolve({
    name: 'orderMainPrint',
    query: {
      id: Base64.encode(formData.value.id+'')
    }
  })
  window.open(url.href, '_blank')
}
 </script>
