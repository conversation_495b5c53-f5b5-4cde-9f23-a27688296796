<template>
   <el-row :gutter="40" class="full-height">
    <el-col :span="4" :xs="24" class="tree-container">
      <ContentWrap class="tree-content">
        <ElTree
            ref="treeRef"
            :data="treeData"
            :props="defaultProps"
            node-key="id"
            :default-expanded-keys="[-1]"
            :expand-on-click-node="true"
            :highlight-current="true"
            :current-node-key="currentTestGrp"
            @node-click="changeTestGrp"
            class="custom-tree"
          />
        </ContentWrap>
      </el-col>
    <el-col :span="19" :xs="24">
        <div id="gc-designer-container">
        <!--
          <el-row style="text-align: right">
            <el-col>
            <el-button
              style="margin-right: 20px; margin-top: 10px"
              @click="saveDetail"
              type="primary"
              >保 存</el-button
            >
          </el-col>
        </el-row>
        -->

          <el-checkbox-group
          v-model="checkedParts"
          @change="handleCheckedPartsChange"
          :disabled="locked"
        >
          <el-checkbox v-for="partsdo in partsLists" :key="partsdo.id" :label="partsdo.classCode">
            {{ partsdo.classDesc }}
          </el-checkbox>
        </el-checkbox-group>
        
          <GcSpreadSheets class="spread-container"  @workbookInitialized="workbookInitialized" v-if="spreadjsload"/>
      </div>
    </el-col>
  </el-row>
</template>

<style>
.spread-container {
  height: 100vh;
}

.tree-container {
  height: 100vh; /* 使用视口高度 */
  display: flex;
  flex-direction: column;
}

.tree-content {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.custom-tree {
  flex: 1;
  height: calc(100vh - 80px); /* 减去头部和其他元素的高度 */
  overflow-y: auto;
}

/* 滚动条美化 */
.custom-tree::-webkit-scrollbar {
  width: 6px;
}

.custom-tree::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

.custom-tree::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

</style>

<script setup lang="ts">
import { ElTree } from 'element-plus'
import * as TestGrpApi from '@/api/salesorder/testgrp'
import '@grapecity-software/spread-sheets/styles/gc.spread.sheets.excel2013white.css'
import * as GC from '@grapecity-software/spread-sheets'
import '@grapecity-software/spread-sheets-resources-zh'
// import '@grapecity-software/spread-sheets-print'
// import '@grapecity-software/spread-sheets-shapes'
// import '@grapecity-software/spread-sheets-io'
// import '@grapecity-software/spread-sheets-pivot-addon'
// import '@grapecity-software/spread-sheets-tablesheet'
import { GcSpreadSheets } from '@grapecity-software/spread-sheets-vue'
import * as detailApi from '@/api/salesorder/detail'
import { ElMessageBox } from 'element-plus'
import { number } from 'vue-types'

// 添加 SpreadJS 授权码配置导入
import { SPREADJS_LICENSE_KEY } from '@/config/spreadjs.config'
GC.Spread.Common.CultureManager.culture("zh-cn")
// 设置 SpreadJS 授权码
GC.Spread.Sheets.LicenseKey = SPREADJS_LICENSE_KEY

const props = defineProps({
  id: {
    type: number,
    // required: true
  }
})

const locked = ref(false)
const spreadjsload = ref(false)
const beforeCheckedParts = ref()
const checkedParts = ref()
const checkedPartsArr = ref()
const partsLists = ref()
const currentTestGrp = ref(1)
// let workbook=mySpread.getHost()
// GC.Spread.Sheets.findControl(this.workbook)

const handleCheckedPartsChange = (values: string[]) => {
  const map = new Map();
  checkedParts.value=[];
  if(beforeCheckedParts.value.length>0)
  {
    values.forEach(function(value) {
      
      // const partsExists = beforeCheckedParts.value.find(tab => tab === value);
      let beforeindex = beforeCheckedParts.value.indexOf(value);
      if(beforeindex!=-1)
      {
        var array = sheet.getArray(0, tableColumns.length+beforeindex*colInfos.length, sheet.getRowCount(), colInfos.length);
        map.set(value, array);
        
      }
    });
  }

  sheet.deleteColumns(tableColumns.length,sheet.getColumnCount()-tableColumns.length);//删除原来列
  
  var index=0;
  var partsArr=new Array();
  partsLists.value.forEach(function(partsdo, index2) {
    const partsExists = values.find(tab => tab === partsdo.classCode);
    if(partsExists)
    {
      checkedParts.value.push(partsdo.classCode);
      sheet.addColumns(tableColumns.length+index*colInfos.length, colInfos.length);
      sheet.addSpan(0, tableColumns.length+index*colInfos.length, 1, colInfos.length, GC.Spread.Sheets.SheetArea.colHeader);
      sheet.setValue(0, tableColumns.length+index*colInfos.length, partsdo.classDesc, GC.Spread.Sheets.SheetArea.colHeader);
      
      showColumnFields(index,partsdo);
      
      if(map.has(partsdo.classCode))
      {
        sheet.setArray(0, tableColumns.length+index*colInfos.length, map.get(partsdo.classCode));
      }

      index++;
      partsArr.push(partsdo)
      
    }


  })
  
  
  
  
  


  sheet.getRange(0,tableColumns.length,sheet.getRowCount(),sheet.getColumnCount()-tableColumns.length).locked(locked.value);
  checkedPartsArr.value=partsArr;
  beforeCheckedParts.value = checkedParts.value
  
}


const treeRef = ref<InstanceType<typeof ElTree>>()
const treeData = ref()

const defaultProps = {
  children: 'children',
  label: 'label',
}

/* 获取列表树 */
const getList = async () => {
  const list = await TestGrpApi.getTestGrpTreeListNoHead()
  treeData.value = list

  locked.value=true
  
}



watch(
  () => props.id,
  () => (
    getList(),

    setTimeout(() => {
        spreadjsload.value=true
    }, 10)
  
  ),
  { immediate: true }
)


const changeTestGrp = (  _event, data, _node, _nodeRef) => {
  if(data.data.id==currentTestGrp.value)
  {
    return
  }
  
  if(data.data.children.length==0)
  {
    var rnFlag=false
    var arr=sheet.getArray(0, tableColumns.length-1, sheet.getRowCount(), 1)
    //判断是不是有RN
    for(var i=0;i<arr.length;i++)
    {
      if(arr[i][0]!=null)
      {
        rnFlag=true
        break
      }
    }

    if(rnFlag)
    {

      currentTestGrp.value = data.data.id
      showSpreads(data.data.id,data.data.partsflg)

    }
    else
    {
      ElMessageBox.alert(
        '未有有效行，请确认是否都为空行？',
        '提示',
        {
          confirmButtonText: 'OK',
          type: 'warning',
        }
      )
    }
    
  }


}

const spreadNS = GC.Spread.Sheets;
let sheet;
let mySpread;
let oldF;
var datasource, colInfos,tableColumns,formulas

var dateTimeStyle = new GC.Spread.Sheets.Style();
dateTimeStyle.cellButtons = [{
    imageType: GC.Spread.Sheets.ButtonImageType.dropdown,
    command: "openDateTimePicker",
    useButtonStyle: false,
    
}];
dateTimeStyle.dropDowns = [{
    type: GC.Spread.Sheets.DropDownType.dateTimePicker,
    option: {
        showTime: false,
        showDateRange: false,
        formatString: "yyyy-MM-dd"
    }
}];
dateTimeStyle.formatter = "yyyy-MM-dd";



const showSpreads =(testgrpid,partsflg)=>{
      beforeCheckedParts.value=[]
      checkedParts.value=[]
      sheet.reset();
  
      detailApi.getDetail(testgrpid,props.id).then((res) => {

        // var dv1 = new GC.Spread.Sheets.DataValidation.createListValidator("Fruit,Vegetable,Food");
        // dv1.inputTitle("Please choose a category:");
        // dv1.inputMessage("Fruit\nVegetableVegetable\nFood");
        // dv1.ignoreBlank(false);
        // sheet.setDataValidator(0, 2, dv1);
        // mySpread.options.highlightInvalidData = true;

        // datasource = res.datasource
        partsLists.value=res.partsList

        colInfos = res.colInfos
        tableColumns=res.tableColumns
        checkedParts.value=res.checkedParts
        checkedPartsArr.value=res.checkedPartsArr
        beforeCheckedParts.value=res.checkedParts
        var tableData=res.tableData
        formulas=res.formulas
        
        

        sheet.autoGenerateColumns = true
        

        sheet.options.isProtected = true
        sheet.options.protectionOptions.allowFilter=true
        sheet.options.protectionOptions.allowResizeColumns=true
        sheet.options.protectionOptions.allowResizeRows=true

        sheet.options.protectionOptions.allowOutlineColumns=true
        sheet.options.protectionOptions.allowOutlineRows=true

        sheet.setRowCount(tableData.length==0?10:tableData.length);

        if(partsflg==0)
        {
          sheet.setColumnCount(tableColumns.length+colInfos.length*checkedParts.value.length);
          // sheet.frozenColumnCount(tableColumns.length);
          sheet.setRowCount(2, GC.Spread.Sheets.SheetArea.colHeader);

        }
        else
        {
          sheet.setColumnCount(tableColumns.length+colInfos.length);
        }
        
        for(var i=0;i<tableColumns.length;i++)
        {
          if(partsflg==0)
          {
            sheet.addSpan(0, i, 2, 1, GC.Spread.Sheets.SheetArea.colHeader);//合并列头
          }
          

          sheet.setValue(0, i, tableColumns[i].displayName, GC.Spread.Sheets.SheetArea.colHeader);
          sheet.setColumnWidth(i, tableColumns[i].width, GC.Spread.Sheets.SheetArea.viewport);
        }


        // sheet.setColumnCount(colInfos.length);
        // sheet.setDataSource(datasource)
        if(partsflg==1)
        {
          showColumnFields(0,null);
          
        }
        else
        {
          
          checkedPartsArr.value.forEach((partsdo,index)=>{
            // sheet.addColumns(tableColumns.length+index*colInfos.length, colInfos.length);
            sheet.addSpan(0, tableColumns.length+index*colInfos.length, 1, colInfos.length, GC.Spread.Sheets.SheetArea.colHeader);
            sheet.setValue(0, tableColumns.length+index*colInfos.length, partsdo.classDesc, GC.Spread.Sheets.SheetArea.colHeader);

            showColumnFields(index,partsdo)
          })


        }
        

        sheet.setArray(0, 0, tableData);

        
        sheet.getRange(0,tableColumns.length,sheet.getRowCount(),sheet.getColumnCount()-tableColumns.length).locked(locked.value);
        
        

        sheet.getRange(-1, -1, sheet.getRowCount(), sheet.getColumnCount(), GC.Spread.Sheets.SheetArea.viewport).wordWrap(true);
        var filter = new spreadNS.Filter.HideRowFilter(new spreadNS.Range(0, 0, sheet.getRowCount(), sheet.getColumnCount()));
        sheet.rowFilter(filter);

        sheet.setColumnVisible(tableColumns.length-1,false,GC.Spread.Sheets.SheetArea.viewport);

        // setFormula();

        // setCellStyle(res.cellStyles);
      })

}



function showColumnFields(index,partsdo)
{
      for(var i=0;i<colInfos.length;i++)
      {
        var index2=i+tableColumns.length+index*colInfos.length;
        sheet.setValue(partsdo==null?0:1, index2, colInfos[i].displayName, GC.Spread.Sheets.SheetArea.colHeader);
        sheet.setColumnWidth(index2, colInfos[i].width, GC.Spread.Sheets.SheetArea.viewport);
        if(colInfos[i].fillType==='date')
        {
          sheet.setStyle(-1,index2,dateTimeStyle);
          sheet.setFormatter(-1, index2, "yyyy-MM-dd");
          // sheet.getRange(0,index2,sheet.getRowCount(),1).setStyle(dateTimeStyle);
          // sheet.getRange(-1, index2, -1 ,1).formatter("yyyy-MM-dd");        
          
        }
        else if(colInfos[i].fillType==='select'||colInfos[i].fillType==='testitem')
        {
          var verticalStyle = new GC.Spread.Sheets.Style();
          verticalStyle.cellButtons = [
              {
                  imageType: GC.Spread.Sheets.ButtonImageType.dropdown,
                  command: "openList",
                  useButtonStyle: false,
              }
          ];

            verticalStyle.dropDowns = [
              {
                  type: GC.Spread.Sheets.DropDownType.list,
                  option: {
                      // valueType: GC.Spread.Sheets.DropdownListValue.array,
                      // layout: {
                      //   displayAs:GC.Spread.Sheets.LayoutDisplayAs.popup
                      // },
                      multiSelect: colInfos[i].isMultiple!=null&&colInfos[i].isMultiple==0?true:false,
                      items:colInfos[i].fillType==='select'?colInfos[i].selectOptions2:colInfos[i].testitems2[partsdo.classCode]
                  }
              }
          ];

          sheet.setStyle(-1, index2, verticalStyle);
          
        }
        // else if(colInfos[i].fillType==='select'||colInfos[i].fillType==='selectE')
        // {
        //   var cellType2 = new GC.Spread.Sheets.CellTypes.ComboBox();
          
        //   cellType2.items(colInfos[i].selectOptions);
        //   if(colInfos[i].fillType==='selectE')
        //   {
        //     cellType2.editable(true);
        //   }
        //   sheet.getRange(-1, index2, -1 ,1).cellType(cellType2);

          

          
        // }
        // else if(colInfos[i].fillType==='testitem')
        // {
        //   var cellType2 = new GC.Spread.Sheets.CellTypes.ComboBox();
        //   cellType2.items(colInfos[i].testitems2[partsdo.classCode]);
        //   cellType2.editorValueType(GC.Spread.Sheets.CellTypes.EditorValueType.value);
        //   sheet.getRange(-1, index2, -1 ,1).cellType(cellType2);
          
        // }

      }


}


function workbookInitialized(spread) {


    mySpread=GC.Spread.Sheets.findControl(spread.getHost())
    sheet = mySpread.getActiveSheet()
    mySpread.options.newTabVisible=0
    mySpread.options.tabEditable = false
    mySpread.options.allowAutoExtendFilterRange = true

   
    // spread.options.showGridLine = true
    // spread.setSheetVisible(0, true)
    // spread.newTabVisible=false
    
    oldF = mySpread.contextMenu.onOpenMenu
    showSpreads(currentTestGrp.value,1)
}
</script>