<template>
  <div class="full-width-container">
    <div class="full-width-wrapper">
      <el-row style="text-align: right;margin-right: 5%;">
          <el-col>
              <!-- <el-button
                style="margin-top: 10px;"
                @click="submitForm(0)"
                type="primary"
                :disabled="formLoading"
                >保 存</el-button
              > -->
              <el-button
                style="margin-right: 5%; margin-top: 10px;"
                @click="submitForm(1)"
                type="primary"
                :disabled="formLoading"
                >提 交</el-button
              >
        </el-col>
      </el-row>
    </div>


    <div class="scroll-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="138px"
        v-loading="formLoading"
      >
            <el-row>
              <el-col
                colspan="2"
                style="text-align: center; font-weight: bold; font-size: 24px;"
                >合同评审变更流程</el-col
              >
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="提交人" prop="submitter">
                  {{ userStore.user?.nickname }} {{ userStore.user?.deptName }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="提交日期" prop="submitdate">
                  {{ currentDate }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-collapse v-model="activeNames" style="margin-left: 5%;margin-right: 5%;">
                <el-collapse-item title="一.合同基本信息" name="1">
                  <el-row>
                    <el-col :span="12">
                        <el-form-item label="销售订单号" prop="salesorder">
                          <el-input v-model="formData.salesorder" disabled/>
                        </el-form-item>                        
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="参考销售订单号" prop="reforder">
                        <el-input v-model="formData.reforder" @change="getBySalerOrder()" />
                      </el-form-item>
                      <!-- <el-row>
                        <el-col :span="20">
                          <el-form-item label="报价单号" prop="quoteno">
                            <el-input v-model="formData.quoteno" :disabled="true">
                              <template #append>
                                <el-button :icon="Close" @click="clearQuotationForm()"/>
                              </template>
                            </el-input>
                          </el-form-item>
                        </el-col>

                        <el-col :span="4">
                            <el-button type="text" style="margin-left: 1px" @click="openQuotationForm()">
                                    报价单号
                            </el-button>
                        </el-col>

                      </el-row> -->
                      
                    </el-col>

                  </el-row>
                  
                  <el-row>
                    <el-col :span="12">
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="项目编号" prop="prjno">
                              <el-input v-model="formData.prjno" :disabled="true">
                                <template #append>
                                  <el-button :icon="Close" @click="clearPrjForm()"/>
                                </template>
                              </el-input>
                            </el-form-item>
                          </el-col>

                          <el-col :span="4">
                              <el-button type="text" style="margin-left: 1px" @click="openPrjForm()">
                                      项目名称
                              </el-button>
                          </el-col>
                        </el-row>


                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="项目名称" prop="prjname">
                          <el-input v-model="formData.prjname" disabled />
                        </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">            
                      <el-row>
                        <el-col :span="20">
                          <el-form-item label="客户代码" prop="customercode">
                            <el-input v-model="formData.customercode" :disabled="true">
                              <template #append>
                                <el-button :icon="Close" @click="clearCtmForm()"/>
                              </template>
                            </el-input>
                          </el-form-item>
                        </el-col>

                        <el-col :span="4">
                            <el-button type="text" style="margin-left: 1px" @click="openCtmForm()">
                                    客户
                            </el-button>
                        </el-col>
                      </el-row>                   
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="客户名称" prop="customername">
                        <el-input v-model="formData.customername" disabled/>
                      </el-form-item>
                    </el-col>                                  
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="客户订单号" prop="ctmorderno">
                        <el-input v-model="formData.ctmorderno" />
                      </el-form-item>
                    </el-col>     


                    <el-col :span="12">
                        <el-form-item label="付款方式" prop="payway">
                          <el-input v-model="formData.payway" />
                        </el-form-item>
                      </el-col>          
                  </el-row>     
                      
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="客户下单确认日期" prop="podate">
                        <el-date-picker
                          v-model="formData.podate"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="请求交货期" prop="delydate">
                        <el-date-picker
                          key="hellodely"
                          v-model="formData.delydate"
                          type="date"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="币种" prop="currency">
                        <el-select v-model="formData.currency" >
                          <el-option
                            v-for="(dict, $index) in getStrDictOptions(DICT_TYPE.SALESORDER_BZ)"
                            :key="$index"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-form-item>          
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="订单金额" prop="orderamt">
                              <el-input-number :precision="2" v-model="formData.orderamt" />
                        </el-form-item>
                      
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="协调评审" prop="teamwork">
                        <el-select
                          v-model="formData.teamwork"
                          filterable
                          :filter-method="filterOptions"
                          clearable
                          multiple
                          style="width: 100%"
                        >
                          <el-option
                            v-for="item in filteredOptions"
                            :key="item.id"
                            :label="item.nickname"
                            :value="item.id"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="线下评审" prop="offlineReview">
                        <el-select v-model="formData.offlineReview">
                          <el-option label="是" :value="1" />
                          <el-option label="否" :value="0" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="线下评审" prop="offlineReview">
                        <el-select v-model="formData.offlineReview" >
                          <el-option
                            v-for="(dict, $index) in getStrDictOptions(DICT_TYPE.SALESORDER_BRAND)"
                            :key="$index"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                          
                </el-collapse-item>


                <el-collapse-item title=" 二.合同附件" name="2">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="技术条款附件" prop="techfiles">
                        <UploadFile v-model="formData.techfiles" />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="合同附件" prop="orderfiles">
                        <UploadFile v-model="formData.orderfiles" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-collapse-item>


                <el-collapse-item title=" 三、合同要求" name="3">
                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="交货时间"   prop="delayremark">
                        <el-input :rows="5" type="textarea" v-model="formData.delayremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="技术、质量要求"   prop="techremark">
                        <el-input :rows="5" type="textarea" v-model="formData.techremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="检验、验收要求"   prop="inspecremark">
                        <el-input :rows="5" type="textarea" v-model="formData.inspecremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="标志商标"   prop="markremark">
                        <el-input :rows="5" type="textarea" v-model="formData.markremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="油漆要求、包装要求"   prop="paintremark">
                        <el-input :rows="5" type="textarea" v-model="formData.paintremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="文件要求"   prop="docremark">
                        <el-input :rows="5" type="textarea" v-model="formData.docremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>


                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="特殊要求"   prop="specialremark">
                        <el-input :rows="5" type="textarea" v-model="formData.specialremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>


                  <el-row>
                    <el-col :span="20">
                      <el-form-item label="其他约定"   prop="otherremark" >
                        <el-input :rows="5" type="textarea" v-model="formData.otherremark" resize="none"/>
                      </el-form-item>
                    </el-col>
                  </el-row>

                </el-collapse-item>

                <!-- <el-collapse-item title=" 四，销售订单号" name="4">
                  <el-row>
                    <el-col :span="12">
                        
                    </el-col>                
                  </el-row>
                </el-collapse-item> -->
            </el-collapse>
      </el-form>
    </div>

    <!-- <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template> -->
  </div>


  <CustomerPop ref="ctmForm" @row-selected="handleRowSelected" />

  <CustomerPrjPop ref="prjForm" @row-selected="handlePrjSelected" />

  <QuotationPop ref="quotationForm" @row-selected="handleQuotationSelected" />
</template>


<style>
.el-form-item
{
  margin-bottom: 10px;
}

.item .el-form-item__label {
  color: red;
}


/* 全宽容器 */
.full-width-container {
  background-color: #fff;
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 全宽包装器 - 使头部扩展到视口边缘 */
.full-width-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}


/* 可滚动内容区域 */
.scroll-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px; /* 与头部对齐 */
}

.scroll-content::-webkit-scrollbar {
  width: 6px;
}

.el-tabs__header{
  margin: 0;
}
</style>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderMainWfApi, OrderMainWfVO } from '@/api/salesorder/ordermainwf'
import { OrderMainApi, OrderMainVO } from '@/api/salesorder/ordermain'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore() // 获取用户信息
import { number, string } from 'vue-types'
import { Close } from '@element-plus/icons-vue'
import CustomerPop from '@/views/salesorder/customerinfo/customerPop.vue'
import CustomerPrjPop from '@/views/salesorder/customerprj/CustomerPrjPop.vue'
import QuotationPop from '@/views/salesorder/quotationinfo/quotationPop.vue'
import * as UserApi from '@/api/system/user'
import { Base64 } from 'js-base64'

const filteredOptions=ref([])
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
const activeNames = ref(['1','2','3','4'])

/** 合同评审流程 表单 */
defineOptions({ name: 'OrderMainBgWfCreate' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// const changefieldsFlg = ref({
// reforderflg:false,
// customernameflg:false,
// customercodeflg:false,
// ctmordernoflg:false,
// quotenoflg:false,
// prjnoflg:false,
// prjnameflg:false,
// podateflg:false,
// delydateflg:false,
// currencyflg:false,
// orderamtflg:false,
// paywayflg:false,
// techfilesflg:false,
// orderfilesflg:false,
// techremarkflg:false,
// inspecremarkflg:false,
// markremarkflg:false,
// paintremarkflg:false,
// docremarkflg:false,
// specialremarkflg:false,
// otherremarkflg:false,
// salesorderflg:false

// }) // 字段的改变
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  processInstanceId: undefined,
  requestno: undefined,
  submitter: undefined,
  department: undefined,
  submitdate: undefined,
  reforder: undefined,
  customername: undefined,
  customercode: undefined,
  ctmorderno: undefined,
  quoteno: undefined,
  prjno: undefined,
  prjname: undefined,
  podate: undefined,
  delydate: undefined,
  currency: undefined,
  orderamt: undefined,
  payway: undefined,
  techfiles: undefined,
  orderfiles: undefined,
  techremark: undefined,
  inspecremark: undefined,
  markremark: undefined,
  paintremark: undefined,
  docremark: undefined,
  specialremark: undefined,
  otherremark: undefined,
  salesorder: undefined,
  teamwork:undefined,
  delayremark:undefined,
  mid: undefined,
  modifytype:undefined,
  offlineReview:undefined
})
const formRules = reactive({
  customername: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
  customercode: [{ required: true, message: '客户代码不能为空', trigger: 'blur' }],
  ctmorderno: [{ required: true, message: '客户订单号不能为空', trigger: 'blur' }],
  quoteno: [{ required: true, message: '报价单号不能为空', trigger: 'blur' }],
  prjno: [{ required: true, message: '项目编号不能为空', trigger: 'blur' }],
  prjname: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
  podate: [{ required: true, message: '客户下单确认日期不能为空', trigger: 'blur' }],
  delydate: [{ required: true, message: '请求交货期不能为空', trigger: 'blur' }],
  currency: [{ required: true, message: '币种不能为空', trigger: 'change' }],
  orderamt: [{ required: true, message: '订单金额不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref



/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async (submittype: number) => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderMainWfVO

    if (formData.value.id) {
      await OrderMainWfApi.updateOrderMainWf(data)
      message.success(t('common.updateSuccess'))

    } else {
      formData.value.id=await OrderMainWfApi.createOrderMainWf(data)
      message.success(t('common.createSuccess'))
    }

    if(submittype==1)
    {
      const processInstanceId=await OrderMainWfApi.createbgwf(formData.value.id)
      window.location.href = '/bpm/process-instance/detail?id='+processInstanceId
    }
    // dialogVisible.value = false
    // 发送操作成功的事件
    // emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    processInstanceId: undefined,
    requestno: undefined,
    submitter: undefined,
    department: undefined,
    submitdate: undefined,
    reforder: undefined,
    customername: undefined,
    customercode: undefined,
    ctmorderno: undefined,
    quoteno: undefined,
    prjno: undefined,
    prjname: undefined,
    podate: undefined,
    delydate: undefined,
    currency: undefined,
    orderamt: undefined,
    payway: undefined,
    techfiles: undefined,
    orderfiles: undefined,
    techremark: undefined,
    inspecremark: undefined,
    markremark: undefined,
    paintremark: undefined,
    docremark: undefined,
    specialremark: undefined,
    otherremark: undefined,
    salesorder: undefined,
    teamwork:undefined,
    delayremark:undefined,
    mid: undefined,
    modifytype: undefined
  }
  formRef.value?.resetFields()
}



const currentDate = ref()
onMounted(async () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = (today.getMonth() + 1).toString().padStart(2, '0')
  const day = today.getDate().toString().padStart(2, '0')
  currentDate.value = `${year}-${month}-${day}`

  // 获得用户列表
  userOptions.value = await UserApi.getSimpleUserNoSupplyList()
  filteredOptions.value = userOptions.value

  open()

})





const handleRowSelected = (row) => {

  formData.value.customercode = row.customerCode
  formData.value.customername = row.customerName

}



const clearCtmForm = () => {
    formData.value.customercode = undefined
    formData.value.customername = undefined 
}



const ctmForm = ref() // 表单 Ref
const openCtmForm = () => {
  ctmForm.value.open()
  // ctmForm.value.getList()
}

const prjForm = ref()
const openPrjForm = () => {
  prjForm.value.openPrj()

}


const clearPrjForm = () => {
    formData.value.prjno = undefined
    formData.value.prjname = undefined
}

const handlePrjSelected = async(row) => {
    formData.value.prjno = row.prjno
    formData.value.prjname = row.prjname

    formData.value.customercode = row.customerCode
    formData.value.customername = row.customerName

}


const quotationForm = ref()
const openQuotationForm = () => {
  quotationForm.value.openQuotation()

}


const clearQuotationForm = () => {
    formData.value.quoteno = undefined
}

const handleQuotationSelected = async(row) => {

    formData.value.quoteno = row.quotationNo
    formData.value.prjname = row.prjname
    formData.value.prjno = row.prjno
    formData.value.customercode = row.customerCode
    formData.value.customername = row.customerName

}

const filterOptions=(query)=>{
  if (!query) {
      filteredOptions.value = userOptions.value;
      return;
  }
  filteredOptions.value = userOptions.value.filter(item =>
    item.nickname.toLowerCase().includes(query.toLowerCase())
  );
}


const getBySalerOrder = async () => {
  const reforder = formData.value.reforder;
  if (!reforder) return;

  try {
    const res = await OrderMainApi.getBySaleOrder(reforder);
    if(res)
    {
      formData.value=res  
    }
    else
    {
      message.error('未找到相关订单信息')
    }
  } catch (error) {
    console.error('Failed to fetch data:', error);
  }
};


// const props = defineProps({
//   id: {
//     type: string,
//     // required: true
//   }
// })

const router = useRouter()
const open= async ()=>{
  resetForm()
  const eid=router.currentRoute.value.query.id
  if(eid)
  {
    const mid=Base64.decode(eid)
    if (!isNaN(mid) && Number.isInteger(Number(mid))) {
      formLoading.value = true
      try {
        formData.value =  await OrderMainApi.getOrderMain(mid)
        formData.value.mid=formData.value.id
        formData.value.id=undefined
        formData.value.submitter = userStore.user?.id
        formData.value.department = userStore.user?.deptId
        formData.value.submitdate = currentDate.value
        formData.value.modifytype='u'
      } finally {
        formLoading.value = false
      }
    }
    else
    {
      ElMessage.error('参数错误')
    }
  }


}

// watch(
//   () => props.id,
//   () => (
//     open()
  
//   ),
//   { immediate: true }
// )


</script>
