<template>
    <div class="full-width-container">
    <div class="full-width-wrapper">
      <el-row style="text-align: right;margin-right: 5%;">
          <el-col>
              <el-button
                style="margin-top: 10px;"
                @click="print()"
                type="primary"
                :disabled="formLoading"
                >打印</el-button
              >
        </el-col>
      </el-row>
    </div>
    <div class="scroll-content">
      <el-tabs tab-position="left" @tab-click="handleTabClick" >
        <el-tab-pane label="订单信息">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="138px"
              v-loading="formLoading"
            >
                  <el-row>
                    <el-col
                      colspan="2"
                      style="text-align: center; font-weight: bold; font-size: 24px;"
                      >合同评审流程</el-col
                    >
                  </el-row>
                  <!-- <el-row>
                    <el-col colspan="2" :span="20">
                      <el-form-item label="评审单号" prop="requestNo" >
                        <span>{{ formData.requestno }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row> -->
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="提交人" prop="submitter">
                        <span>{{ submitterName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="提交日期" prop="submitdate">
                        <span>{{ formData.submitdate }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-collapse v-model="activeNames" style="margin-left: 5%;margin-right: 5%;">
                      <el-collapse-item title="一.合同基本信息" name="1">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="销售订单号" prop="salesorder">
                                <el-input v-model="formData.salesorder" disabled/>
                            </el-form-item>

                          </el-col>

                          <el-col :span="12">

                            <el-form-item label="参考销售订单号" prop="reforder">
                              <el-input v-model="formData.reforder" disabled/>
                            </el-form-item>
                            <!-- <el-row>
                              <el-col :span="20">
                                <el-form-item label="报价单号" prop="quoteno">
                                  <el-input v-model="formData.quoteno" :disabled="true">
                                    <template #append>
                                      <el-button :icon="Close" @click="clearQuotationForm()" :disabled="disabled"/>
                                    </template>
                                  </el-input>
                                </el-form-item>
                              </el-col>

                              <el-col :span="4">
                                  <el-button type="text" style="margin-left: 1px" @click="openQuotationForm()" :disabled="disabled">
                                          报价单号
                                  </el-button>
                              </el-col>

                            </el-row> -->
                            
                          </el-col>

                        </el-row>
                        
                        <el-row>
                          <el-col :span="12">
                              <el-row>
                                <el-col :span="20">
                                  <el-form-item label="项目编号" prop="prjno">
                                    <el-input v-model="formData.prjno" :disabled="true">
                                      <template #append>
                                        <el-button :icon="Close" @click="clearPrjForm()" :disabled="disabled"/>
                                      </template>
                                    </el-input>
                                  </el-form-item>
                                </el-col>

                                <el-col :span="4">
                                    <el-button type="text" style="margin-left: 1px" @click="openPrjForm()" :disabled="disabled">
                                            项目名称
                                    </el-button>
                                </el-col>
                              </el-row>


                          </el-col>

                          <el-col :span="12">
                              <el-form-item label="项目名称" prop="prjname">
                                <el-input v-model="formData.prjname" disabled />
                              </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="12">            
                            <el-row>
                              <el-col :span="20">
                                <el-form-item label="客户代码" prop="customercode">
                                  <el-input v-model="formData.customercode" :disabled="true">
                                    <template #append>
                                      <el-button :icon="Close" @click="clearCtmForm()" :disabled="disabled"/>
                                    </template>
                                  </el-input>
                                </el-form-item>
                              </el-col>

                              <el-col :span="4">
                                  <el-button type="text" style="margin-left: 1px" @click="openCtmForm()" :disabled="disabled">
                                          客户
                                  </el-button>
                              </el-col>
                            </el-row>                   
                          </el-col>

                          <el-col :span="12">
                            <el-form-item label="客户名称" prop="customername">
                              <el-input v-model="formData.customername" disabled/>
                            </el-form-item>
                          </el-col>                                  
                        </el-row>

                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="客户订单号" prop="ctmorderno">
                              <el-input v-model="formData.ctmorderno" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>     


                          <el-col :span="12">
                              <el-form-item label="付款方式" prop="payway">
                                <el-input v-model="formData.payway" :disabled="disabled"/>
                              </el-form-item>
                            </el-col>          
                        </el-row>     
                            
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="客户下单确认日期" prop="podate">
                              <el-date-picker
                                v-model="formData.podate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                :disabled="disabled"
                              />
                            </el-form-item>
                          </el-col>

                          <el-col :span="12">
                            <el-form-item label="请求交货期" prop="delydate">
                              <el-date-picker
                                key="hellodely"
                                v-model="formData.delydate"
                                type="date"
                                value-format="YYYY-MM-DD"
                                :disabled="disabled"
                              />
                            </el-form-item>
                          </el-col>
                        </el-row>

                        
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="币种" prop="currency">
                              <el-select v-model="formData.currency" :disabled="disabled">
                                <el-option
                                  v-for="(dict, $index) in getStrDictOptions(DICT_TYPE.SALESORDER_BZ)"
                                  :key="$index"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>          
                          </el-col>

                          <el-col :span="12">
                              <el-form-item label="订单金额" prop="orderamt">
                                    <el-input-number :precision="2" v-model="formData.orderamt" :disabled="disabled"/>
                              </el-form-item>
                            
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="协调评审" prop="teamwork">
                              <el-select
                                v-model="formData.teamwork"
                                filterable
                                :filter-method="filterOptions"
                                clearable
                                multiple
                                style="width: 100%"
                                :disabled="disabled"
                              >
                                <el-option
                                  v-for="item in filteredOptions"
                                  :key="item.id"
                                  :label="item.nickname"
                                  :value="item.id"
                                />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="线下评审" prop="offlineReview">
                              <el-select v-model="formData.offlineReview">
                                <el-option label="是" :value="1" />
                                <el-option label="否" :value="0" />
                              </el-select>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="商标" prop="brand">
                              <el-select v-model="formData.brand" >
                                <el-option
                                  v-for="(dict, $index) in getStrDictOptions(DICT_TYPE.SALESORDER_BRAND)"
                                  :key="$index"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </el-col>
                          <el-col :span="10">
                            <el-form-item label="收款方式" prop="recvName">
                              <el-input v-model="formData.recvName" :disabled="true">
                                <template #append>
                                  <el-button :icon="Close" @click="clearRecvForm()" :disabled="disabled"/>
                                </template>
                              </el-input>
                            </el-form-item>
                          </el-col>

                          <el-col :span="2">
                            <el-button type="text" style="margin-left: 1px" @click="openRecvForm()" :disabled="disabled">
                              收款方式
                            </el-button>
                          </el-col>
                        </el-row>

                                
                      </el-collapse-item>


                      <el-collapse-item title=" 二.合同附件" name="2">
                        <el-row>
                          <el-col :span="12">
                            <el-form-item label="技术条款附件" prop="techfiles">
                              <UploadFile v-model="formData.techfiles" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>

                          <el-col :span="12">
                            <el-form-item label="合同附件" prop="orderfiles">
                              <UploadFile v-model="formData.orderfiles" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-collapse-item>


                      <el-collapse-item title=" 三、合同要求" name="3">
                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="交货时间"   prop="delayremark">
                              <el-input :rows="5" type="textarea" v-model="formData.delayremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="技术、质量要求"   prop="techremark">
                              <el-input :rows="5" type="textarea" v-model="formData.techremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="检验、验收要求"   prop="inspecremark">
                              <el-input :rows="5" type="textarea" v-model="formData.inspecremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="标志商标"   prop="markremark">
                              <el-input :rows="5" type="textarea" v-model="formData.markremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="油漆要求、包装要求"   prop="paintremark">
                              <el-input :rows="5" type="textarea" v-model="formData.paintremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="文件要求"   prop="docremark">
                              <el-input :rows="5" type="textarea" v-model="formData.docremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>


                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="特殊要求"   prop="specialremark">
                              <el-input :rows="5" type="textarea" v-model="formData.specialremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>


                        <el-row>
                          <el-col :span="20">
                            <el-form-item label="其他约定"   prop="otherremark">
                              <el-input :rows="5" type="textarea" v-model="formData.otherremark" resize="none" :disabled="disabled"/>
                            </el-form-item>
                          </el-col>
                        </el-row>

                      </el-collapse-item>

                      <!-- <el-collapse-item title=" 四，销售订单号" name="4">
                        <el-row>
                          <el-col :span="12">

                          </el-col>                
                        </el-row>
                      </el-collapse-item> -->
                  </el-collapse>
            </el-form>
        </el-tab-pane>

        <el-tab-pane label="特殊要求" >
          <detailVue ref="detailRef" :id="props.id" :viewType=disabled v-if="detailShow"/>
        </el-tab-pane>

        <el-tab-pane label="汇总">
          <summaryVue :id="props.id" v-if="summaryShow" />
        </el-tab-pane>

      </el-tabs>
    </div>
  </div>



  <CustomerPop ref="ctmForm" @row-selected="handleRowSelected" />

  <CustomerPrjPop ref="prjForm" @row-selected="handlePrjSelected" />

  <QuotationPop ref="quotationForm" @row-selected="handleQuotationSelected" />

  <RecvtermPop ref="recvForm" @row-selected="handleRecvSelected" />

</template>


<style>
.el-form-item
{
  margin-bottom: 10px;
}
.item .el-form-item__label {
  color: red;
}

.el-tabs--border-card>.el-tabs__content
{
  padding: 5px;
}

/* 全宽容器 */
.full-width-container {
  background-color: #fff;
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 全宽包装器 - 使头部扩展到视口边缘 */
.full-width-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}


/* 可滚动内容区域 */
.scroll-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0px; /* 与头部对齐 */
}

.scroll-content::-webkit-scrollbar {
  width: 6px;
}

.el-tabs__header{
  margin: 0;
}

.el-tabs__item{
  padding: 0 10px;
}

/* .el-tabs__content > .el-tab-pane:nth-child(2) {
  height: 70vh;
} */
</style>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderMainWfApi, OrderMainWfVO } from '@/api/salesorder/ordermainwf'
import detailVue from './detail.vue'
import summaryVue from '../summary.vue'
import { number } from 'vue-types'
import { Close } from '@element-plus/icons-vue'
import CustomerPop from '@/views/salesorder/customerinfo/customerPop.vue'
import CustomerPrjPop from '@/views/salesorder/customerprj/CustomerPrjPop.vue'
import QuotationPop from '@/views/salesorder/quotationinfo/quotationPop.vue'
import RecvtermPop from '@/views/salesorder/recvterm/recvtermPop.vue'
import * as UserApi from '@/api/system/user'
import * as DeptApi from '@/api/system/dept'
import * as ProcessInstanceApi from '@/api/bpm/processInstance'

const filteredOptions=ref<UserApi.UserVO[]>([])
const userOptions = ref<UserApi.UserVO[]>([]) // 用户列表
const activeNames = ref(['1','2','3','4'])
const submitterName=ref('') // 提交人

/** 合同评审流程 表单 */
defineOptions({ name: 'SalesView' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const disabled = ref(true)


const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: undefined,
  processInstanceId: undefined,
  requestno: undefined,
  submitter: undefined,
  department: undefined,
  submitdate: undefined,
  reforder: undefined,
  customername: undefined,
  customercode: undefined,
  ctmorderno: undefined,
  quoteno: undefined,
  prjno: undefined,
  prjname: undefined,
  podate: undefined,
  delydate: undefined,
  currency: undefined,
  orderamt: undefined,
  payway: undefined,
  techfiles: undefined,
  orderfiles: undefined,
  techremark: undefined,
  inspecremark: undefined,
  markremark: undefined,
  paintremark: undefined,
  docremark: undefined,
  specialremark: undefined,
  otherremark: undefined,
  salesorder: undefined,
  teamwork:undefined,
  delayremark:undefined,
  mid: undefined,
  modifytype:undefined,
  offlineReview: undefined,
  brand: undefined,
  recvName: undefined,
  recvCode: undefined,
})
const formRules = reactive({
  customername: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
  customercode: [{ required: true, message: '客户代码不能为空', trigger: 'blur' }],
  ctmorderno: [{ required: true, message: '客户订单号不能为空', trigger: 'blur' }],
  quoteno: [{ required: true, message: '报价单号不能为空', trigger: 'blur' }],
  prjno: [{ required: true, message: '项目编号不能为空', trigger: 'blur' }],
  prjname: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
  podate: [{ required: true, message: '客户下单确认日期不能为空', trigger: 'blur' }],
  delydate: [{ required: true, message: '请求交货期不能为空', trigger: 'blur' }],
  currency: [{ required: true, message: '币种不能为空', trigger: 'change' }],
  orderamt: [{ required: true, message: '订单金额不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref



/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  let isValid = false;
  try {
    isValid = await formRef.value.validate(); // 返回 true 或 false
  } catch (error) {
    isValid = false;
  }

  if (!isValid) {
    message.error('请检查表单项后再提交');
    return { success: false, message: '表单验证未通过' };
  }

  formLoading.value = true;
  try {
    const data = formData.value as unknown as OrderMainWfVO;
    await OrderMainWfApi.updateOrderMainWf(data);
    return { success: true, message: '提交成功' };
  } catch (error) {
    console.error('提交失败:', error);
    return { success: false, message: `提交失败：${error.message}` };
  } finally {
    formLoading.value = false;
  }
};

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    processInstanceId: undefined,
    requestno: undefined,
    submitter: undefined,
    department: undefined,
    submitdate: undefined,
    reforder: undefined,
    customername: undefined,
    customercode: undefined,
    ctmorderno: undefined,
    quoteno: undefined,
    prjno: undefined,
    prjname: undefined,
    podate: undefined,
    delydate: undefined,
    currency: undefined,
    orderamt: undefined,
    payway: undefined,
    techfiles: undefined,
    orderfiles: undefined,
    techremark: undefined,
    inspecremark: undefined,
    markremark: undefined,
    paintremark: undefined,
    docremark: undefined,
    specialremark: undefined,
    otherremark: undefined,
    salesorder: undefined,
    teamwork:undefined,
    delayremark:undefined,
    mid: undefined,
    modifytype: undefined
  }
  formRef.value?.resetFields()
}




const props = defineProps({
  id: {
    type: number,
    // required: true
  }
})





const open= async ()=>{
    resetForm()
    if (props.id) {
      formLoading.value = true
      try {
        formData.value =await OrderMainWfApi.getOrderMainWf(props.id)

        // var changefields=res.changefields

        // Object.keys(changefields).forEach((key) => {
        //   changefieldsFlg[key] = changefields[key]
        // })

        const userInfo=await UserApi.getUser(formData.value.submitter)
        submitterName.value=userInfo.nickname

        if(formData.value.department!=null)
        {
          const deptInfo=await DeptApi.getDept(formData.value.department)
          submitterName.value=userInfo.nickname+'  '+deptInfo.name
        }


        const param = {
          processInstanceId: formData.value.processInstanceId
        }
        const data = await ProcessInstanceApi.getApprovalDetail(param)
        if(data.todoTask==null)
        {
            disabled.value=true
            if(formData.value.teamwork!=null)
            {
              var teamwork=formData.value.teamwork.split(",")
              for(var i=0;i<teamwork.length;i++)
              {
                  const userInfo=await UserApi.getUser(teamwork[i])
                  filteredOptions.value.push(userInfo)
              }
            }
        }
        else
        {
          disabled.value=false
          // 获得用户列表
          userOptions.value = await UserApi.getSimpleUserNoSupplyList()
          filteredOptions.value = userOptions.value
        }


      } finally {
        formLoading.value = false
      }
    }

}



watch(
  () => props.id,
  () => (
    open()
  
  ),
  { immediate: true }
)




const handleRowSelected = (row) => {

  formData.value.customercode = row.customerCode
  formData.value.customername = row.customerName

}



const clearCtmForm = () => {
    formData.value.customercode = undefined
    formData.value.customername = undefined 
}



const ctmForm = ref() // 表单 Ref
const openCtmForm = () => {
  ctmForm.value.open()
  // ctmForm.value.getList()
}

const prjForm = ref()
const openPrjForm = () => {
  prjForm.value.openPrj()

}

const recvForm = ref() // 表单 Ref
const openRecvForm = () => {
  recvForm.value.open()
  // ctmForm.value.getList()
}

const clearPrjForm = () => {
    formData.value.prjno = undefined
    formData.value.prjname = undefined
}

const handlePrjSelected = async(row) => {
    formData.value.prjno = row.id
    formData.value.prjname = row.prjname

    formData.value.customercode = row.customerCode
    formData.value.customername = row.customerName

}

const clearRecvForm = () => {
  formData.value.recvCode = undefined
  formData.value.recvName = undefined
}

const handleRecvSelected = async(row) => {
  formData.value.recvName = row.name
  formData.value.recvCode = row.code

  formData.value.recvName = row.name
  formData.value.recvCode = row.code

}

const quotationForm = ref()
const openQuotationForm = () => {
  quotationForm.value.openQuotation()

}


const clearQuotationForm = () => {
    formData.value.quoteno = undefined
}

const handleQuotationSelected = async(row) => {

    formData.value.quoteno = row.quotationNo
    formData.value.prjname = row.prjname
    formData.value.prjno = row.prjno
    formData.value.customercode = row.customerCode
    formData.value.customername = row.customerName

}

const filterOptions=(query)=>{
  if (!query) {
      filteredOptions.value = userOptions.value;
      return;
  }
  filteredOptions.value = userOptions.value.filter(item =>
    item.nickname.toLowerCase().includes(query.toLowerCase())
  );
}

const detailShow=ref(false)
const summaryShow=ref(false)


import { ElMessage, type TabsPaneContext } from 'element-plus'

const handleTabClick = (tab: TabsPaneContext, event: Event) => {
  summaryShow.value = false
  if(tab.props?.label=='特殊要求')
  {
    detailShow.value = true
  }
  else if(tab.props?.label=='汇总')
  {
    summaryShow.value = true
  }
  console.log(tab)
}


const detailRef = ref()
const saveData = async () => {
  const result = await submitForm();

  if (result.success) {
    //message.success('主表单提交成功');
  } else {
    message.error(result.message || '提交失败');
    throw new Error(result.message);
  }

    // 继续调用子模块
    if (detailRef.value && typeof detailRef.value.validate === 'function') {
    const detailResult = await detailRef.value.validate();
    if (!detailResult || !detailResult.success) {
      message.error(detailResult?.message || '子模块验证失败');
      throw new Error('子模块验证失败');
    }
  }


  // 继续调用子模块
  if (detailRef.value && typeof detailRef.value.saveDetail === 'function') {
    const detailResult = await detailRef.value.saveDetail();
    if (!detailResult || !detailResult.success) {
      message.error(detailResult?.message || '子模块保存失败');
      throw new Error('子模块保存失败');
    }
  }

  //message.success('全部保存成功');
};

//  暴露保存数据接口供其他组件调用
defineExpose({
  save: saveData
})

const { resolve } = useRouter()
const print =()=>{ 
  const url = resolve({
    name: 'orderMainWfPrint',
    query: {
      processInstanceId: formData.value.processInstanceId
    }
  })
  window.open(url.href, '_blank')
}
</script>
