<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1080px" style="height:485px;">
    <ContentWrap>
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
      >

        <el-form-item label="收款方式" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入收款方式代码"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="客户名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入客户名称"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>


        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>


    <el-table
      :data="tableData"
      style="width: 100%"
      highlight-current-row
      @current-change="handleCurrentChange"
    >
      <el-table-column width="60" label="序号" align="center" type="index"/>
      <el-table-column prop="code" label="收款方式代码" width="280" />
      <el-table-column prop="name" label="收款方式名称" width="600" />
    </el-table>

    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      :page-sizes="[5]"
      @pagination="getList"
    />

  </Dialog>

</template>
<script lang="ts" setup>
import {RecvTermApi} from "@/api/salesorder/recvterm";

defineOptions({ name: 'SystemUserForm' })

const total = ref(0) // 列表的总页数
const loading = ref(true) // 列表的加载中

// const { t } = useI18n() // 国际化
const tableData = ref([])
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const currentRow = ref()
const emit = defineEmits(['row-selected']);

//table表定义
interface Customer {
  id: bigint
  name: string
  code: bigint

}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 5,
  name: undefined,
  code: undefined,
  status:0
})

const handleCurrentChange = (val: Customer | undefined) => {
  currentRow.value = val
  console.log('currentRow.value:' + currentRow.value.customerName)
  if (val) {
    emit('row-selected',val);
    dialogVisible.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RecvTermApi.getRecvTermPage(queryParams)
    tableData.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 打开弹窗 */
const open = async () => {
  dialogVisible.value = true
  dialogTitle.value = '客户浏览框'
  queryParams.code = undefined
  queryParams.name = undefined

  getList()
  // formType.value = type
  // // 加载部门树
  // deptList.value = handleTree(await DeptApi.getSimpleDeptList())
  // // 加载岗位列表
  // postList.value = await PostApi.getSimplePostList()
}

const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const queryFormRef = ref() // 搜索的表单


defineExpose({
  // getList,
  open
})

</script>
