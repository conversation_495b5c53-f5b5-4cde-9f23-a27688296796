<template>
  <div class="upload-box">
    <!-- 多文件上传及图片列表 -->
    <div class="file-list">
      <div
        v-for="(file, index) in fileList"
        :key="file.url + index"
        class="file-item"
      >
        <img :src="file.url" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div class="handle-icon" @click="handleFilePreview(file)">
            <Icon icon="ep:zoom-in" />
            <span>查看</span>
          </div>
          <div v-if="!disabled" class="handle-icon" @click="handleRemove(file)">
            <Icon icon="ep:delete" />
            <span>删除</span>
          </div>
        </div>
      </div>
      <!-- 新增按钮，只有未超出数量限制时显示 -->
      <el-upload
        v-if="!disabled && fileList.length < limit"
        :id="uuid + '-multi'"
        :accept="fileType.join(',')"
        :action="uploadUrl"
        :before-upload="beforeUpload"
        :class="['upload', drag ? 'no-border' : '']"
        :disabled="disabled"
        :drag="drag"
        :http-request="httpRequest"
        :multiple="true"
        :on-error="uploadError"
        :on-exceed="handleExceed"
        :on-success="uploadSuccess"
        :show-file-list="false"
      >
        <div class="upload-empty">
          <slot name="empty">
            <Icon icon="ep:plus" />
          </slot>
        </div>
      </el-upload>
    </div>

    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadFile, UploadProps, UploadUserFile } from 'element-plus'

import { generateUUID } from '@/utils'
import { propTypes } from '@/utils/propTypes'
import { useUpload,usePreview } from '@/components/UploadFile/src/useUpload'
import { createImageViewer } from '@/components/ImageViewer'

const { getMinioUrl } = usePreview()

defineOptions({ name: 'UploadImg' })

type FileTypes =
  | 'image/apng'
  | 'image/bmp'
  | 'image/gif'
  | 'image/jpeg'
  | 'image/pjpeg'
  | 'image/png'
  | 'image/svg+xml'
  | 'image/tiff'
  | 'image/webp'
  | 'image/x-icon'

// 接受父组件参数
const props = defineProps({
  modelValue: {
    type: [Array] as PropType<UploadUserFile[]>,
    default: () => []
  },
  multiple: propTypes.bool.def(true), // 是否支持多文件上传 ==> 非必传（默认为 false）
  drag: propTypes.bool.def(true), // 是否支持拖拽上传 ==> 非必传（默认为 true）
  disabled: propTypes.bool.def(false), // 是否禁用上传组件 ==> 非必传（默认为 false）
  limit: propTypes.number.def(5), // 最大图片上传数 ==> 非必传（默认为 5张）
  fileSize: propTypes.number.def(5), // 图片大小限制 ==> 非必传（默认为 5M）
  fileType: propTypes.array.def(['image/jpeg', 'image/png', 'image/gif']), // 图片类型限制 ==> 非必传（默认为 ["image/jpeg", "image/png", "image/gif"]）
  height: propTypes.string.def('150px'), // 组件高度 ==> 非必传（默认为 150px）
  width: propTypes.string.def('150px'), // 组件宽度 ==> 非必传（默认为 150px）
  borderradius: propTypes.string.def('8px'), // 组件边框圆角 ==> 非必传（默认为 8px）
  showDelete: propTypes.bool.def(true), // 是否显示删除按钮（仅单文件模式）
  showBtnText: propTypes.bool.def(true) // 是否显示按钮文字（仅单文件模式）
})
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
// 生成组件唯一id
const uuid = ref('id-' + generateUUID())

// 单文件模式相关
const currentFile = ref<UploadUserFile | null>(null)
const displayValue = ref<string>('')
const singleFileList = ref<UploadUserFile[]>([])

// 多文件模式相关
const fileList = ref<UploadUserFile[]>([])

// 查看图片
const imagePreview = async (imgUrl: string, version?: string) => {
  // const minioUrl = await getMinioUrl(imgUrl, version || '1')
  createImageViewer({
    zIndex: 9999999,
    urlList: [imgUrl]
  })
}

// 多文件模式下的图片预览
const handleFilePreview = async (file: UploadUserFile) => {
  if (file.url) {
    await imagePreview(file.url, file.version || '1')
  }
}

const emit = defineEmits(['update:modelValue'])

// 单文件删除
const deleteImg = () => {
  currentFile.value = null
  displayValue.value = ''
  singleFileList.value = []
  emit('update:modelValue', [])
}

// 多文件删除
const handleRemove = (uploadFile: UploadUserFile) => {
  const index = fileList.value.findIndex(
    (item) => item.url === uploadFile.url && item.name === uploadFile.name
  )
  if (index > -1) {
    fileList.value.splice(index, 1)
    // 直接发送更新，避免调用emitUpdateModelValue导致循环
    const currentFiles = fileList.value.map(file => ({
      name: file.name,
      url: file.url,
      version: file.version,
      etag: file.etag
    }))
    emit('update:modelValue', currentFiles)
  }
}

const { uploadUrl, httpRequest } = useUpload()

const editImg = () => {
  const dom = document.querySelector(`#${uuid.value} .el-upload__input`)
  dom && dom.dispatchEvent(new MouseEvent('click'))
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
  const imgType = props.fileType
  if (!imgType.includes(rawFile.type as FileTypes))
    message.notifyWarning('上传图片不符合所需的格式！')
  if (!imgSize) message.notifyWarning(`上传图片大小不能超过 ${props.fileSize}M！`)
  
  return imgType.includes(rawFile.type as FileTypes) && imgSize
}

// 处理传入的值（单文件模式）
const processModelValue = async (value: UploadUserFile[]) => {
  if (!value || value.length === 0) {
    currentFile.value = null
    displayValue.value = ''
    singleFileList.value = []
    return
  }

  // 单文件模式只取第一个文件
  const file = value[0]
  currentFile.value = file
  if (file.url) {
    // 如果URL已经是预授权后的URL，直接使用
    // 否则进行预授权处理
    if (file.url.startsWith('http')) {
      displayValue.value = file.url
    } else {
      const minioUrl = await getMinioUrl(file.url, file.version || '1')
      displayValue.value = minioUrl
    }
  } else {
    displayValue.value = ''
  }
  singleFileList.value = [file]
}

// 处理传入的值（多文件模式）
const processModelValueMultiple = async (val: UploadUserFile[]) => {
  if (!val || val.length === 0) {
    fileList.value = []
    return
  }

  // 直接使用传入的文件列表，不进行预授权处理
  // 预授权处理在上传成功时进行，避免无限循环
  fileList.value = [...val]
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  async (newValue) => {
    if (props.multiple) {
      await processModelValueMultiple(newValue)
    } else {
      await processModelValue(newValue)
    }
  },
  { immediate: true, deep: true }
)

// 监听文件列表变化（仅用于调试，实际不触发更新）
// watch(
//   () => fileList.value,
//   () => {
//     if (props.multiple) {
//       console.log('文件列表变化:', fileList.value)
//     }
//   },
//   { deep: true }
// )

// 图片上传成功处理
const uploadSuccess: UploadProps['onSuccess'] = async (res: any): Promise<void> => {
  message.success('上传成功')
  
  if (props.multiple) {
    // 多文件模式处理 - 创建UploadUserFile对象并获取预授权URL
    const uploadUserFile: UploadUserFile = {
      name: res.data.name || 'image',
      url: res.data.url,
      version: res.data.version || '1',
      etag: res.data.etag
    }
    
    // 获取预授权URL
    if (uploadUserFile.url) {
      const minioUrl = await getMinioUrl(uploadUserFile.url, uploadUserFile.version || '1')
      uploadUserFile.url = minioUrl
    }
    
    // 添加到文件列表
    fileList.value.push(uploadUserFile)
    // 直接发送更新，避免调用emitUpdateModelValue导致循环
    const currentFiles = fileList.value.map(file => ({
      name: file.name,
      url: file.url,
      version: file.version,
      etag: file.etag
    }))
    emit('update:modelValue', currentFiles)
  } else {
    // 单文件模式处理
    const uploadUserFile: UploadUserFile = {
      name: res.data.name || 'image',
      url: res.data.url,
      version: res.data.version || '1',
      etag: res.data.etag
    }
    
    // 获取预授权URL
    if (uploadUserFile.url) {
      const minioUrl = await getMinioUrl(uploadUserFile.url, uploadUserFile.version || '1')
      uploadUserFile.url = minioUrl
    }
    
    currentFile.value = uploadUserFile
    singleFileList.value = [uploadUserFile]
    emit('update:modelValue', [uploadUserFile])
  }
}

// 发送图片链接列表更新（仅单文件模式使用）
const emitUpdateModelValue = () => {
  emit('update:modelValue', singleFileList.value)
}

// 图片上传错误提示
const uploadError = () => {
  message.notifyError('图片上传失败，请您重新上传！')
}

// 文件数超出提示（多文件模式）
const handleExceed = () => {
  message.notifyWarning(`当前最多只能上传 ${props.limit} 张图片，请移除后上传！`)
}
</script>
<style lang="scss" scoped>
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger),
    :deep(.el-upload--picture-card) {
      border: 1px dashed var(--el-color-danger) !important;

      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger,
  .el-upload--picture-card {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border: 1px dashed var(--el-border-color-darker) !important;

    &:hover {
      border: 1px dashed var(--el-border-color-darker) !important;
    }
  }
}

.upload-box {
  .no-border {
    :deep(.el-upload),
    :deep(.el-upload--picture-card) {
      border: none !important;
    }
  }

  :deep(.upload) {
    .el-upload {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderradius);
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);

        .upload-handle {
          opacity: 1;
        }
      }

      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: v-bind(borderradius);

        &:hover {
          border: 1px dashed var(--el-color-primary);
        }
      }

      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }

      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .upload-empty {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-info);

        .el-icon {
          font-size: 28px;
          color: var(--el-text-color-secondary);
        }
      }

      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: rgb(0 0 0 / 60%);
        opacity: 0;
        box-sizing: border-box;
        transition: var(--el-transition-duration-fast);
        align-items: center;
        justify-content: center;

        .handle-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 6%;
          color: aliceblue;

          .el-icon {
            margin-bottom: 40%;
            font-size: 130%;
            line-height: 130%;
          }

          span {
            font-size: 85%;
            line-height: 85%;
          }
        }
      }
    }

    // 多文件模式样式
    .el-upload-list__item,
    .el-upload--picture-card {
      width: v-bind(width);
      height: v-bind(height);
      background-color: transparent;
      border-radius: v-bind(borderradius);
    }

    .el-upload-list__item {
      &:hover {
        .upload-handle {
          opacity: 1;
        }
      }
    }

    .upload-handle {
      .handle-icon {
        .el-icon {
          margin-bottom: 15%;
          font-size: 140%;
        }

        span {
          font-size: 100%;
        }
      }
    }
  }

  .el-upload__tip {
    line-height: 18px;
    text-align: center;
  }

  // 多文件列表样式
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;

    .file-item {
      position: relative;
      width: v-bind(width);
      height: v-bind(height);
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderradius);
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);

        .upload-handle {
          opacity: 1;
        }
      }

      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: rgb(0 0 0 / 60%);
        opacity: 0;
        box-sizing: border-box;
        transition: var(--el-transition-duration-fast);
        align-items: center;
        justify-content: center;

        .handle-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 6%;
          color: aliceblue;

          .el-icon {
            margin-bottom: 15%;
            font-size: 140%;
          }

          span {
            font-size: 100%;
          }
        }
      }
    }
  }
}
</style>