import { Layout } from '@/utils/routerHelper'
import { useUserStore } from '@/store/modules/user'
const { t } = useI18n()
/**
 * redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
 hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

 alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 只有一个时，会将那个子路由当做根路由显示在侧边栏，
 若你想不管路由下面的 children 声明的个数都显示你的根路由，
 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 一直显示根路由(默认 false)

 title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

 icon: 'svg-name'          设置该路由的图标

 noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

 breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

 affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

 noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

 activeMenu: '/dashboard'  显示高亮的路由路径

 followAuth: '/dashboard'  跟随哪个路由进行权限过滤

 canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)
 }
 **/
const remainingRouter: AppRouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/index',
    name: 'Home',
    meta: {},
    children: [
      {
        path: 'index',
        component: () => {
          const userStore = useUserStore()
          const parentId = userStore.user.parentdeptId
          if (parentId === 1000) {
            return import('@/views/Home/SupplierHome.vue')
          } else {
            return import('@/views/Home/Index.vue')
          }
        },
        name: 'Index',
        meta: {
          title: t('router.home'),
          icon: 'ep:home-filled',
          noCache: false,
          affix: true
        }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    name: 'UserInfo',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'profile',
        component: () => import('@/views/Profile/Index.vue'),
        name: 'Profile',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:user',
          title: t('common.profile')
        }
      },
      {
        path: 'notify-message',
        component: () => import('@/views/system/notify/my/index.vue'),
        name: 'MyNotifyMessage',
        meta: {
          canTo: true,
          hidden: true,
          noTagsView: false,
          icon: 'ep:message',
          title: '我的站内信'
        }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    name: 'dict',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'type/data/:dictType',
        component: () => import('@/views/system/dict/data/index.vue'),
        name: 'SystemDictData',
        meta: {
          title: '字典数据',
          noCache: true,
          hidden: true,
          canTo: true,
          icon: '',
          activeMenu: '/system/dict'
        }
      }
    ]
  },

  {
    path: '/codegen',
    component: Layout,
    name: 'CodegenEdit',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'edit',
        component: () => import('@/views/infra/codegen/EditTable.vue'),
        name: 'InfraCodegenEditTable',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '修改生成配置',
          activeMenu: 'infra/codegen/index'
        }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    name: 'JobL',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'job-log',
        component: () => import('@/views/infra/job/logger/index.vue'),
        name: 'InfraJobLog',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '调度日志',
          activeMenu: 'infra/job/index'
        }
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/special-login',
    component: () => import('@/views/Login/SpecialLogin.vue'),
    name: 'SpecialLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/supplier-register',
    name: 'CreateGyssq',
    meta: {
      title: '供应商申请',
      hidden: true,
      noTagsView: true
    },
    component: () => import('@/views/srm-manage/workflow/gyssq/create.vue')
  },
  {
    path: '/supplier-update',
    name: 'GysbgForm',
    meta: {
      title: '供应商信息变更',
      hidden: true,
      noTagsView: true
    },
    component: () => import('@/views/srm-manage/workflow/gysbg/create.vue')
  },
  {
    path: '/gysxxgfpjDemo',
    name: 'GysxxGfpj',
    meta: {
      title: '供方评价demo',
      hidden: true,
      noTagsView: true
    },
    component: () => import('@/views/srm-manage/zsj/gysxxgfpj/index.vue')
  },
  {
    path: '/sso',
    component: () => import('@/views/Login/Login.vue'),
    name: 'SSOLogin',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/social-login',
    component: () => import('@/views/Login/SocialLogin.vue'),
    name: 'SocialLogin',
    meta: {
      hidden: true,
      title: t('router.socialLogin'),
      noTagsView: true
    }
  },
  {
    path: '/403',
    component: () => import('@/views/Error/403.vue'),
    name: 'NoAccess',
    meta: {
      hidden: true,
      title: '403',
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFound',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/500',
    component: () => import('@/views/Error/500.vue'),
    name: 'Error',
    meta: {
      hidden: true,
      title: '500',
      noTagsView: true
    }
  },
  {
    path: '/bpm',
    component: Layout,
    name: 'bpm',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'manager/form/edit',
        component: () => import('@/views/bpm/form/editor/index.vue'),
        name: 'BpmFormEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程表单',
          activeMenu: '/bpm/manager/form'
        }
      },
      {
        path: 'manager/model/edit',
        component: () => import('@/views/bpm/model/editor/index.vue'),
        name: 'BpmModelEditor',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '设计流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'manager/simple/model',
        component: () => import('@/views/bpm/simple/SimpleModelDesign.vue'),
        name: 'SimpleModelDesign',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '仿钉钉设计流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'manager/definition',
        component: () => import('@/views/bpm/definition/index.vue'),
        name: 'BpmProcessDefinition',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程定义',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'oa/leave/create',
        component: () => import('@/views/bpm/oa/leave/create.vue'),
        name: 'OALeaveCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'oa/leave/detail',
        component: () => import('@/views/bpm/oa/leave/detail.vue'),
        name: 'OALeaveDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 OA 请假',
          activeMenu: '/bpm/oa/leave'
        }
      },
      {
        path: 'oa/customer/create',
        component: () => import('@/views/salesorder/customerwf/create.vue'),
        name: 'CreateCustomer',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '发起 客户 新建/修改',
          activeMenu: '/bpm/oa/customer'
        }
      },
      {
        path: 'oa/customer/detail',
        component: () => import('@/views/salesorder/customerwf/detail.vue'),
        name: 'DetailCustomer',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 客户 新建/修改',
          activeMenu: '/bpm/oa/customer'
        }
      },
      {
        path: 'oa/commission/create',
        component: () => import('@/views/salesorder/commission/create.vue'),
        name: 'CreateCommission',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 佣金 审批',
          activeMenu: '/bpm/oa/commission'
        }
      },
      {
        path: 'oa/commission/detail',
        component: () => import('@/views/salesorder/commission/detail.vue'),
        name: 'DetailCommission',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 佣金 审批',
          activeMenu: '/bpm/oa/commission'
        }
      },
      {
        path: 'oa/commission/change/create',
        component: () => import('@/views/salesorder/commission/change/create.vue'),
        name: 'CreateCommissionChange',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 佣金变更 审批',
          activeMenu: '/bpm/oa/commission/change'
        }
      },
      {
        path: 'oa/commission/change/detail',
        component: () => import('@/views/salesorder/commission/change/detail.vue'),
        name: 'DetailCommissionChange',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 佣金变更 审批',
          activeMenu: '/bpm/oa/commission/change'
        }
      },
      {
        path: 'oa/payment/create',
        component: () => import('@/views/salesorder/payment/create.vue'),
        name: 'CreatePayment',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 应收账款核销 审批',
          activeMenu: '/bpm/oa/payment'
        }
      },
      {
        path: 'oa/payment/detail',
        component: () => import('@/views/salesorder/payment/detail.vue'),
        name: 'DetailPayment',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 应收账款核销 审批',
          activeMenu: '/bpm/oa/payment'
        }
      },
      {
        path: 'oa/discountFee/create',
        component: () => import('@/views/salesorder/discountFee/create.vue'),
        name: 'CreateDiscountFee',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 坏账及折扣费用申请流程 审批',
          activeMenu: '/bpm/oa/discountFee'
        }
      },
      {
        path: 'oa/discountFee/detail',
        component: () => import('@/views/salesorder/discountFee/detail.vue'),
        name: 'DetailDiscountFee',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 坏账及折扣费用申请流程 审批',
          activeMenu: '/bpm/oa/discountFee'
        }
      },
      {
        path: 'oa/quotation/create',
        component: () => import('@/views/salesorder/quotation/create.vue'),
        name: 'CreateQuotation',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 报价审核流程 审批',
          activeMenu: '/bpm/oa/quotation'
        }
      },
      {
        path: 'oa/quotation/detail',
        component: () => import('@/views/salesorder/quotation/detail.vue'),
        name: 'DetailQuotation',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 报价审核流程 审批',
          activeMenu: '/bpm/oa/quotation'
        }
      },
      {
        path: 'oa/salesInvoice/create',
        component: () => import('@/views/salesorder/salesInvoice/create.vue'),
        name: 'CreateSalesInvoice',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 销售开票流程 审批',
          activeMenu: '/bpm/oa/salesInvoice'
        }
      },
      {
        path: 'oa/salesInvoice/detail',
        component: () => import('@/views/salesorder/salesInvoice/detail.vue'),
        name: 'DetailSalesInvoice',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 销售开票流程 审批',
          activeMenu: '/bpm/oa/salesInvoice'
        }
      },
      {
        path: 'oa/customerprj/create',
        component: () => import('@/views/salesorder/customerprjwf/create.vue'),
        name: 'CreateCustomerPrjWf',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建 客户项目管理流程 审批',
          activeMenu: '/bpm/oa/customerprj'
        }
      },
      {
        path: 'oa/customerprj/detail',
        component: () => import('@/views/salesorder/customerprjwf/detail.vue'),
        name: 'DetailCustomerPrjWf',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '查看 客户项目管理流程 审批',
          activeMenu: '/bpm/oa/customerprj'
        }
      },
      {
        path: 'manager/model/create',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '创建流程',
          activeMenu: '/bpm/manager/model'
        }
      },
      {
        path: 'manager/model/update/:id',
        component: () => import('@/views/bpm/model/form/index.vue'),
        name: 'BpmModelUpdate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '修改流程',
          activeMenu: '/bpm/manager/model'
        }
      }
    ]
  },
  {
    path: '/bpm',
    name: 'bpmDetail',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'process-instance/detail',
        component: () => import('@/views/bpm/processInstance/detail/index.vue'),
        name: 'BpmProcessInstanceDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '流程详情',
          activeMenu: '/bpm/task/my'
        },
        props: (route) => ({
          id: route.query.id,
          taskId: route.query.taskId,
          activityId: route.query.activityId
        })
      }
    ]
  },
  {
    path: '/mall/product', // 商品中心
    component: Layout,
    name: 'ProductCenter',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'spu/add',
        component: () => import('@/views/mall/product/spu/form/index.vue'),
        name: 'ProductSpuAdd',
        meta: {
          noCache: false, // 需要缓存
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '商品添加',
          activeMenu: '/mall/product/spu'
        }
      },
      {
        path: 'spu/edit/:id(\\d+)',
        component: () => import('@/views/mall/product/spu/form/index.vue'),
        name: 'ProductSpuEdit',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:edit',
          title: '商品编辑',
          activeMenu: '/mall/product/spu'
        }
      },
      {
        path: 'spu/detail/:id(\\d+)',
        component: () => import('@/views/mall/product/spu/form/index.vue'),
        name: 'ProductSpuDetail',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: '商品详情',
          activeMenu: '/mall/product/spu'
        }
      },
      {
        path: 'property/value/:propertyId(\\d+)',
        component: () => import('@/views/mall/product/property/value/index.vue'),
        name: 'ProductPropertyValue',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          icon: 'ep:view',
          title: '商品属性值',
          activeMenu: '/product/property'
        }
      }
    ]
  },
  {
    path: '/mall/trade', // 交易中心
    component: Layout,
    name: 'TradeCenter',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'order/detail/:id(\\d+)',
        component: () => import('@/views/mall/trade/order/detail/index.vue'),
        name: 'TradeOrderDetail',
        meta: { title: '订单详情', icon: 'ep:view', activeMenu: '/mall/trade/order' }
      },
      {
        path: 'after-sale/detail/:id(\\d+)',
        component: () => import('@/views/mall/trade/afterSale/detail/index.vue'),
        name: 'TradeAfterSaleDetail',
        meta: { title: '退款详情', icon: 'ep:view', activeMenu: '/mall/trade/after-sale' }
      }
    ]
  },
  {
    path: '/member',
    component: Layout,
    name: 'MemberCenter',
    meta: { hidden: true },
    children: [
      {
        path: 'user/detail/:id',
        name: 'MemberUserDetail',
        meta: {
          title: '会员详情',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/member/user/detail/index.vue')
      }
    ]
  },
  {
    path: '/pay',
    component: Layout,
    name: 'pay',
    meta: { hidden: true },
    children: [
      {
        path: 'cashier',
        name: 'PayCashier',
        meta: {
          title: '收银台',
          noCache: true,
          hidden: true
        },
        component: () => import('@/views/pay/cashier/index.vue')
      }
    ]
  },
  {
    path: '/diy',
    name: 'DiyCenter',
    meta: { hidden: true },
    component: Layout,
    children: [
      {
        path: 'template/decorate/:id',
        name: 'DiyTemplateDecorate',
        meta: {
          title: '模板装修',
          noCache: true,
          hidden: true,
          activeMenu: '/mall/promotion/diy/template'
        },
        component: () => import('@/views/mall/promotion/diy/template/decorate.vue')
      },
      {
        path: 'page/decorate/:id',
        name: 'DiyPageDecorate',
        meta: {
          title: '页面装修',
          noCache: true,
          hidden: true,
          activeMenu: '/mall/promotion/diy/page'
        },
        component: () => import('@/views/mall/promotion/diy/page/decorate.vue')
      }
    ]
  },
  {
    path: '/crm',
    component: Layout,
    name: 'CrmCenter',
    meta: { hidden: true },
    children: [
      {
        path: 'clue/detail/:id',
        name: 'CrmClueDetail',
        meta: {
          title: '线索详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/clue'
        },
        component: () => import('@/views/crm/clue/detail/index.vue')
      },
      {
        path: 'customer/detail/:id',
        name: 'CrmCustomerDetail',
        meta: {
          title: '客户详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/customer'
        },
        component: () => import('@/views/crm/customer/detail/index.vue')
      },
      {
        path: 'business/detail/:id',
        name: 'CrmBusinessDetail',
        meta: {
          title: '商机详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/business'
        },
        component: () => import('@/views/crm/business/detail/index.vue')
      },
      {
        path: 'contract/detail/:id',
        name: 'CrmContractDetail',
        meta: {
          title: '合同详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/contract'
        },
        component: () => import('@/views/crm/contract/detail/index.vue')
      },
      {
        path: 'receivable-plan/detail/:id',
        name: 'CrmReceivablePlanDetail',
        meta: {
          title: '回款计划详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/receivable-plan'
        },
        component: () => import('@/views/crm/receivable/plan/detail/index.vue')
      },
      {
        path: 'receivable/detail/:id',
        name: 'CrmReceivableDetail',
        meta: {
          title: '回款详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/receivable'
        },
        component: () => import('@/views/crm/receivable/detail/index.vue')
      },
      {
        path: 'contact/detail/:id',
        name: 'CrmContactDetail',
        meta: {
          title: '联系人详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/contact'
        },
        component: () => import('@/views/crm/contact/detail/index.vue')
      },
      {
        path: 'product/detail/:id',
        name: 'CrmProductDetail',
        meta: {
          title: '产品详情',
          noCache: true,
          hidden: true,
          activeMenu: '/crm/product'
        },
        component: () => import('@/views/crm/product/detail/index.vue')
      }
    ]
  },
  {
    path: '/ai',
    component: Layout,
    name: 'Ai',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'image/square',
        component: () => import('@/views/ai/image/square/index.vue'),
        name: 'AiImageSquare',
        meta: {
          title: '绘图作品',
          icon: 'ep:home-filled',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/Error/404.vue'),
    name: '',
    meta: {
      title: '404',
      hidden: true,
      breadcrumb: false
    }
  },
  {
    path: '/iot',
    component: Layout,
    name: 'IOT',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'product/detail/:id',
        name: 'IoTProductDetail',
        meta: {
          title: '产品详情',
          noCache: true,
          hidden: true,
          activeMenu: '/iot/product'
        },
        component: () => import('@/views/iot/product/detail/index.vue')
      },
      {
        path: 'device/detail/:id',
        name: 'IoTDeviceDetail',
        meta: {
          title: '设备详情',
          noCache: true,
          hidden: true,
          activeMenu: '/iot/device'
        },
        component: () => import('@/views/iot/device/detail/index.vue')
      }
    ]
  },
  {
    path: '/wfShd/shdprint',
    name: 'Shdprint',
    meta: {
      title: '送货单打印',
      hidden: true,
      noTagsView: true
    },
    component: () => import('@/views/srm-manage/workflow/wfShd/Shdprint.vue')
  },
  {
    path: '/reportTemplate/CgddAPrint',
    component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddAPrint.vue'),
    name: 'CgddAPrint',
    meta: {
      noCache: true,
      hidden: true,
      canTo: true,
      title: '采购合同模板'
    }
  },
  {
    path: '/reportTemplate/CgddBPrint',
    component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddBPrint.vue'),
    name: 'CgddBPrint',
    meta: {
      noCache: true,
      hidden: true,
      canTo: true,
      title: '采购合同模板'
    }
  },
  {
    path: '/GenerateLp/SpreadJSCellEditor',
    component: () => import('@/components/SpreadJSCellEditor.vue'),
    name: 'SpreadJSCellEditor',
    meta: {
      noCache: true,
      hidden: true,
      canTo: true,
      title: '完整Excel模板'
    }
  },
  {
    path: '/reportTemplate/CgddCPrint',
    component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddCPrint.vue'),
    name: 'CgddCPrint',
    meta: {
      noCache: true,
      hidden: true,
      canTo: true,
      title: '采购合同模板'
    }
  },
  {
    path: '/reportTemplate/CgddDPrint',
    component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddDPrint.vue'),
    name: 'CgddDPrint',
    meta: {
      noCache: true,
      hidden: true,
      canTo: true,
      title: '采购合同模板'
    }
  },
  {
    path: '/gysxx/GysPjPrint',
    name: 'GysPjPrint',
    meta: {
      title: '供应商信息打印',
      hidden: true,
      noTagsView: true
    },
    component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/GysPjprint.vue')
  },
  {
    path: '/srm-manage/workflow',
    component: Layout,
    name: 'srm-manage',
    meta: {
      hidden: true
    },
    children: [
      {
        path: '/wfbjdww/bjdwwCreate',
        name: 'BjdWwDjCwjlForm',
        meta: {
          title: '报价单节点',
          hidden: true,
          noTagsView: true
        },
        component: () => import('@/views/srm-manage/workflow/wfbjdww/BjdWwDjCwjlForm.vue')
      },
      {
        path: '/gys/my-orders',
        name: 'MyOrders',
        meta: {
          title: '我的订单',
          hidden: false,
          noTagsView: false
        },
        component: () => import('@/views/srm-manage/workflow/wfShd/myorders.vue')
      },
      {
        path: '/srm-manage/workflow/wfShd/adjust',
        name: 'WfShdAdjust',
        meta: {
          title: '送货数调整',
          hidden: true,
          noTagsView: true
        },
        component: () => import('@/views/srm-manage/workflow/wfShd/ShdTzForm.vue')
      },
      {
        path: '/gysxxpdjlDemo/index',
        name: 'GysxxPdjl',
        meta: {
          title: '评定记录demo',
          hidden: true,
          noTagsView: true
        },
        component: () => import('@/views/srm-manage/zsj/gysxxpdjl/index.vue')
      },
      {
        path: 'cgddbg/reportTemplate/CgddChangeCgyForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgddbg/reportTemplate/CgddChangeCgyForm.vue'),
        name: 'CgddChangeCgyForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单变更采购员',
          activeMenu: '/srm-manage/workflow/cgddbg'
        }
      },

      {
        path: 'cgddbg/reportTemplate/CgddChangeCgjlForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgddbg/reportTemplate/CgddChangeCgjlForm.vue'),
        name: 'CgddChangeCgjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单变更采购经理',
          activeMenu: '/srm-manage/workflow/cgddbg'
        }
      },

      {
        path: 'cgddbg/reportTemplate/CgddChangeFzForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgddbg/reportTemplate/CgddChangeFzForm.vue'),
        name: 'CgddChangeFzForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单变更副总',
          activeMenu: '/srm-manage/workflow/cgddbg'
        }
      },
      {
        path: 'cgddbg/reportTemplate/CgddChangeGysForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgddbg/reportTemplate/CgddChangeGysForm.vue'),
        name: 'CgddChangeGysForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单变更供应商',
          activeMenu: '/srm-manage/workflow/cgddbg'
        }
      },
      {
        path: 'cgddbg/reportTemplate/CgddChangePrint',
        component: () =>
          import('@/views/srm-manage/workflow/cgddbg/reportTemplate/CgddChangePrint.vue'),
        name: 'CgddChangePrint',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单变更打印',
          activeMenu: '/srm-manage/workflow/cgddbg'
        }
      },

      {
        path: 'cgdd/reportTemplate/CgddACgyForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddACgyForm.vue'),
        name: 'CgddACgyForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板A采购员'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddACgjlForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddACgjlForm.vue'),
        name: 'CgddACgjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板A采购经理'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddAFzForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddAFzForm.vue'),
        name: 'CgddAFzForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板A副总'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddAGysForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddAGysForm.vue'),
        name: 'CgddAGysForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板A供应商'
        }
      },

      {
        path: 'cgdd/reportTemplate/CgddBCgyForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddBCgyForm.vue'),
        name: 'CgddBCgyForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板B采购员'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddBCgjlForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddBCgjlForm.vue'),
        name: 'CgddBCgjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板B采购经理'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddBFzForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddBFzForm.vue'),
        name: 'CgddBFzForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板B副总'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddBGysForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddBGysForm.vue'),
        name: 'CgddBGysForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板B供应商'
        }
      },

      {
        path: 'cgdd/reportTemplate/CgddCCgyForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddCCgyForm.vue'),
        name: 'CgddCCgyForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板C采购员'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddCCgjlForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddCCgjlForm.vue'),
        name: 'CgddCCgjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板C采购经理'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddCFzForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddCFzForm.vue'),
        name: 'CgddCFzForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板C副总'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddCGysForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddCGysForm.vue'),
        name: 'CgddCGysForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板C供应商'
        }
      },

      {
        path: 'cgdd/reportTemplate/CgddDCgyForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddDCgyForm.vue'),
        name: 'CgddDCgyForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板D采购员'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddDCgjlForm',
        component: () =>
          import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddDCgjlForm.vue'),
        name: 'CgddDCgjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板D采购经理'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddDFzForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddDFzForm.vue'),
        name: 'CgddDFzForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板D副总'
        }
      },
      {
        path: 'cgdd/reportTemplate/CgddDGysForm',
        component: () => import('@/views/srm-manage/workflow/cgdd/reportTemplate/CgddDGysForm.vue'),
        name: 'CgddDGysForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '采购订单模板D供应商'
        }
      },

      {
        path: 'gyssq/ZhiBaoDetail',
        component: () => import('@/views/srm-manage/workflow/gyssq/ZhiBaoDetail.vue'),
        name: 'GyssqOneForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供应商申请流程质保审批'
        }
      },
      {
        path: 'gyssq/CaiWuDetail',
        component: () => import('@/views/srm-manage/workflow/gyssq/CaiWuDetail.vue'),
        name: 'CaiWuForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供应商申请流程财务审批'
        }
      },
      {
        path: 'gyssq/create',
        component: () => import('@/views/srm-manage/workflow/gyssq/create.vue'),
        name: 'GyssqForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供应商申请创建'
        }
      },
      {
        path: 'gysxxpdjl/index',
        component: () => import('@/views/srm-manage/workflow/gysxxpdjl/index.vue'),
        name: 'WfGysxxPdjl',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '试样测试/评定记录'
        }
      },
      {
        path: 'gysxxpdjl/create',
        component: () => import('@/views/srm-manage/workflow/gysxxpdjl/create.vue'),
        name: 'GysxxPdjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '试样测试/评定记录创建'
        }
      },
      {
        path: 'gysbgpdjl/create',
        component: () => import('@/views/srm-manage/workflow/gysbgpdjl/create.vue'),
        name: 'GysbgPdjlForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '试样测试/评定记录变更创建'
        }
      },
      {
        path: 'gysbg/detailTwo',
        component: () => import('@/views/srm-manage/workflow/gysbg/detailTwo.vue'),
        name: 'GysbgTwoForm',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供应商变更申请流程'
        }
      },
      {
        path: 'wfgysxxgfpj/create',
        component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/GongYingCreate.vue'),
        name: 'WfGysxxGfpjGongYingFrom',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供方评价-供应部'
        }
      },
      {
        path: 'wfgysxxgfpj/procreate',
        component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/ProcessCreate.vue'),
        name: 'WfGysxxGfpjProcessCreate',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供方评价-过程评估'
        }
      },
      {
        path: 'wfgysxxgfpj/jscreate',
        component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/JiShuCreate.vue'),
        name: 'WfGysxxGfpjJishuFrom',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供方评价-技术部'
        }
      },
      {
        path: 'wfgysxxgfpj/zbcreate',
        component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/ZhiBaoCreate.vue'),
        name: 'WfGysxxGfpjZhiBaoFrom',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供方评价-质保部'
        }
      },
      {
        path: 'wfgysxxgfpj/jhcreate',
        component: () => import('@/views/srm-manage/workflow/wfgysxxgfpj/JiHuaCreate.vue'),
        name: 'WfGysxxGfpjJiHuaFrom',
        meta: {
          noCache: true,
          hidden: true,
          canTo: true,
          title: '供方评价-计划部'
        }
      },
    ]
  },

  {
    path: '/srm-manage/qa',
    name: 'qa',
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: 'zsj',
        name: 'zsj',
        meta: { hidden: true },
        children: [
          {
            path: 'xjd',
            name: 'xjd',
            meta: { hidden: true },
            children: [
              {
                path: 'xjd-cz',
                name: 'xjdcz',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysCZBj',
                    component: () => import('@/views/srm-manage/qa/xjdcz/XjdCzBjForm.vue'),
                    name: 'XjdCzBjForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '供应商材质比价'
                    }
                  }
                ]
              },
              {
                path: 'xjd-czgy',
                name: 'xjdczgy',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysCZGYBj',
                    component: () => import('@/views/srm-manage/qa/xjdczgy/XjdCzgyBjForm.vue'),
                    name: 'XjdCzgyBjForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '供应商材质工艺比价'
                    }
                  }
                ]
              },
              {
                path: 'xjd-ljh',
                name: 'xjdljh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysLJHBj',
                    component: () => import('@/views/srm-manage/qa/xjdljh/XjdLjhBjForm.vue'),
                    name: 'XjdLjhBjForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '供应商按料比价'
                    }
                  }
                ]
              },
              {
                path: 'xjd-dh',
                name: 'xjddh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysDHBj',
                    component: () => import('@/views/srm-manage/qa/xjddh/XjdDhBjForm.vue'),
                    name: 'XjdDhBjForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '供应商按单比价'
                    }
                  }
                ]
              },
              {
                path: 'xjd-ww',
                name: 'xjdww',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysDHBj',
                    component: () => import('@/views/srm-manage/qa/xjdww/XjdWwBjForm.vue'),
                    name: 'XjdWwBjForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '供应商委外按单比价'
                    }
                  }
                ]
              }
            ]
          },
          {
            path: 'bjd',
            name: 'bjd',
            meta: { hidden: true },
            children: [
              {
                path: 'bjd-cz',
                name: 'bjdcz',
                meta: { hidden: true },
                children: [
                  {
                    path: 'bjdCzDetailForm',
                    component: () => import('@/views/srm-manage/qa/bjdcz/BjdCzDetailForm.vue'),
                    name: 'BjdCzDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '报价单材质详情'
                    }
                  }
                ]
              },
              {
                path: 'bjd-czgy',
                name: 'bjdczgy',
                meta: { hidden: true },
                children: [
                  {
                    path: 'bjdCzgyDetailForm',
                    component: () => import('@/views/srm-manage/qa/bjdczgy/BjdCzgyDetailForm.vue'),
                    name: 'BjdCzgyDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '报价单材质工艺详情'
                    }
                  }
                ]
              },{
                path: 'bjd-ww',
                name: 'bjdww',
                meta: { hidden: true },
                children: [
                  {
                    path: 'BjdWwDetailForm',
                    component: () => import('@/views/srm-manage/qa/bjdww/BjdWwDetailForm.vue'),
                    name: 'BjdWwDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '报价单委外材质详情'
                    }
                  }
                ]
              },
              {
                path: 'bjd-ljh',
                name: 'bjdljh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'bjdLjhDetailForm',
                    component: () => import('@/views/srm-manage/qa/bjdljh/BjdLjhDetailForm.vue'),
                    name: 'BjdLjhDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '物料详情'
                    }
                  }
                ]
              },
              {
                path: 'bjd-dh',
                name: 'bjddh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'bjdDhDetailForm',
                    component: () => import('@/views/srm-manage/qa/bjddh/BjdDhDetailForm.vue'),
                    name: 'BjdDhDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
              {
                path: 'gysbjd-dh',
                name: 'gysbjddh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysBjdDhDetailForm',
                    component: () => import('@/views/srm-manage/zsj/gysbjd/BjdDhDetailForm.vue'),
                    name: 'GysBjdDhDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
              {
                path: 'gysbjd-ljh',
                name: 'gysbjdljh',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysBjdLjhDetailForm',
                    component: () => import('@/views/srm-manage/zsj/gysbjd/BjdLjhDetailForm.vue'),
                    name: 'GysBjdLjhDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
              {
                path: 'gysbjd-cz',
                name: 'gysbjdcz',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysBjdCzDetailForm',
                    component: () => import('@/views/srm-manage/zsj/gysbjd/BjdCzDetailForm.vue'),
                    name: 'GysBjdCzDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
              {
                path: 'gysbjd-czgyy',
                name: 'gysbjdczgy',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysBjdCzgyDetailForm',
                    component: () => import('@/views/srm-manage/zsj/gysbjd/BjdCzgyDetailForm.vue'),
                    name: 'GysBjdCzgyDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
              {
                path: 'gysbjd-ww',
                name: 'gysbjdww',
                meta: { hidden: true },
                children: [
                  {
                    path: 'gysBjdWwDetailForm',
                    component: () => import('@/views/srm-manage/zsj/gysbjd/BjdWwDetailForm.vue'),
                    name: 'GysBjdWwDetailForm',
                    meta: {
                      noCache: true,
                      hidden: true,
                      canTo: true,
                      title: '单号详情'
                    }
                  }
                ]
              },
            ]
          }
        ]
      }
    ]
  },

{
    path: '/srm-manage/mould',
    component: Layout,
    name: 'mould',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'mouldinfo/CreateMouldPurchase',
        component: () => import('@/views/srm-manage/mould/mouldinfo/CreateMouldPurchase.vue'),
        name: 'CreateMouldPurchase',
        meta: {
          title: '模具采购',
          icon: 'el-icon-s-tools'
        }
      },
      {
        path: 'mouldinfo/CreateMouldTransfer',
        component: () => import('@/views/srm-manage/mould/mouldinfo/CreateMouldTransfer.vue'),
        name: 'CreateMouldTransfer',
        meta: {
          title: '模具调拨',
          icon: 'el-icon-s-tools'
        }
      },
      {
        path: 'mouldinfo/CreateMouldScrap',
        component: () => import('@/views/srm-manage/mould/mouldinfo/CreateMouldScrap.vue'),
        name: 'CreateMouldScrap',
        meta: {
          title: '模具报废',
          icon: 'el-icon-s-tools'
        }
      },
      {
        path: 'moldrepair/index',
        name: 'WfMoldRepair',
        meta: {
          title: '模具维修',
          hidden: true,
          noTagsView: true,
          canTo: true,
        },
        component: () => import('@/views/srm-manage/workflow/moldrepair/index.vue')
      },
      {
        path: 'mouldinfo/wfmoldrepair',
        name: 'WfSqMoldRepairForm',
        meta: {
          title: '模具维修',
          icon: 'el-icon-s-tools',
        },
        component: () => import('@/views/srm-manage/mould/mouldinfo/WfMoldRepair.vue')
      },
      {
        path: 'moldrepair/hasmoldrepair',
        name: 'HasMoldRepairForm',
        meta: {
          title: '所在供应商确认',
          hidden: true,
          noTagsView: true,
          canTo: true,
        },
        component: () => import('@/views/srm-manage/workflow/moldrepair/HasMoldRepair.vue')
      },
      {
        path: 'moldrepair/overmoldrepair',
        name: 'OverMoldRepairForm',
        meta: {
          title: '模具维修确认',
          hidden: true,
          noTagsView: true,
          canTo: true,
        },
        component: () => import('@/views/srm-manage/workflow/moldrepair/OverMoldRepair.vue')
      },
      {
        path: 'moldrepair/transfermoldrepair',
        name: 'TransferMoldRepairForm',
        meta: {
          title: '模具维修确认',
          hidden: true,
          noTagsView: true,
          canTo: true,
        },
        component: () => import('@/views/srm-manage/workflow/moldrepair/TransferMoldRepair.vue')
      },
    ]
  },



  {
    path: '/salesorder',
    // component: Layout,
    name: 'salesorder',
    meta: {
      hidden: true
    },
    children: [
      {
        path: 'ordermainwf',
        name: 'ordermainwf',
        meta: {
          hidden: true
        },
        children: [
          {
            path: 'create',
            component: () => import('@/views/salesorder/ordermainwf/create/create.vue'),
            name: 'OrderMainWfCreate',
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审流程创建'
              // activeMenu: '/salesorder/ordermainwf/salesview'
            }
          },
          {
            path: 'print',
            component: () => import('@/views/salesorder/ordermainwf/create/print.vue'),
            // props: (route) => ({
            //   id: route.query.id
            // }),
            name: 'orderMainWfPrint',
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审流程打印'
              // activeMenu: '/salesorder/ordermainwf/salesview'
            }
          }
        ]
      },
      {
        path: 'ordermainbgwf',
        name: 'ordermainbgwf',
        meta: {
          hidden: true
        },
        children: [
          {
            path: 'create',
            component: () => import('@/views/salesorder/ordermainwf/modify/create.vue'),
            name: 'OrderMainBgWfCreate',
            // props: (route) => ({
            //   id: route.query.id
            // }),
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审变更流程创建'
            }
          },
          {
            path: 'print',
            component: () => import('@/views/salesorder/ordermainwf/modify/print.vue'),
            // props: (route) => ({
            //   id: route.query.id
            // }),
            name: 'orderMainBgWfPrint',
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审变更流程打印'
              // activeMenu: '/salesorder/ordermainwf/salesview'
            }
          }
        ]
      },
      {
        path: 'ordermain',
        name: 'ordermain',
        meta: {
          hidden: true
        },
        children: [
          {
            path: 'view',
            component: () => import('@/views/salesorder/ordermain/view.vue'),
            props: (route) => ({
              id: route.query.id
            }),
            name: 'orderMainView',
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审'
              // activeMenu: '/salesorder/ordermainwf/salesview'
            }
          },
          {
            path: 'print',
            component: () => import('@/views/salesorder/ordermain/print.vue'),
            // props: (route) => ({
            //   id: route.query.id
            // }),
            name: 'orderMainPrint',
            meta: {
              noCache: true,
              hidden: true,
              title: '合同评审打印'
              // activeMenu: '/salesorder/ordermainwf/salesview'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/filepreview',
    // component: Layout,
    name: 'filepreview',
    meta: {
      hidden: true
    },
    component: () => import('@/views/kkFileView/preview.vue')
  },

  {
    path: '/srm-manage/basedata',
    component: Layout,
    name: 'basedata',
    meta: {
      hidden: true
    },

    children: [
      {
      path: 'gcxx/index',
        component: () => import('@/views/srm-manage/basedata/gcxx/index.vue'),
        name: 'Gcxx',
        meta: {
          title: '工厂信息',
          icon: 'el-icon-s-tools'
        }
      }
    ]
  }  

]

export default remainingRouter
