#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 868336 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=169184, tid=169304
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.5******.16-jcef (21.0.5+8) (build 21.0.5+8-b631.16)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.5******.16-jcef (21.0.5+8-b631.16, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://************:8081': 

Host: 11th Gen Intel(R) Core(TM) i5-11500H @ 2.90GHz, 12 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5072)
Time: Mon Dec  9 22:58:00 2024  Windows 10 , 64 bit Build 19041 (10.0.19041.5072) elapsed time: 0.419840 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000287ec102750):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=169304, stack(0x0000009ec2800000,0x0000009ec2900000) (1024K)]


Current CompileTask:
C2:420 1063       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x0000009ec2800000,0x0000009ec2900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e52b9]
V  [jvm.dll+0x8c3633]
V  [jvm.dll+0x8c5b8e]
V  [jvm.dll+0x8c6273]
V  [jvm.dll+0x288f46]
V  [jvm.dll+0xc66dd]
V  [jvm.dll+0xc6c13]
V  [jvm.dll+0x2feb50]
V  [jvm.dll+0x60c609]
V  [jvm.dll+0x2598a2]
V  [jvm.dll+0x259c5f]
V  [jvm.dll+0x252415]
V  [jvm.dll+0x24fc6e]
V  [jvm.dll+0x1cd6a4]
V  [jvm.dll+0x25f5dc]
V  [jvm.dll+0x25db26]
V  [jvm.dll+0x3ff5e6]
V  [jvm.dll+0x86b248]
V  [jvm.dll+0x6e3abd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000287ec7f2320, length=16, elements={
0x000002878804f0b0, 0x00000287ec0ea880, 0x00000287ec0ef7a0, 0x00000287ec0f8590,
0x00000287ec0fb0f0, 0x00000287ec0feb60, 0x00000287ec1019d0, 0x00000287ec102750,
0x00000287ec10d200, 0x00000287ec25df60, 0x00000287ec264200, 0x00000287ec654950,
0x00000287ec90a380, 0x00000287ec60bb70, 0x00000287ec8c9a10, 0x00000287ec862350
}

Java Threads: ( => current thread )
  0x000002878804f0b0 JavaThread "main"                              [_thread_blocked, id=169248, stack(0x0000009ec1a00000,0x0000009ec1b00000) (1024K)]
  0x00000287ec0ea880 JavaThread "Reference Handler"          daemon [_thread_blocked, id=169280, stack(0x0000009ec2200000,0x0000009ec2300000) (1024K)]
  0x00000287ec0ef7a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=169284, stack(0x0000009ec2300000,0x0000009ec2400000) (1024K)]
  0x00000287ec0f8590 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=169288, stack(0x0000009ec2400000,0x0000009ec2500000) (1024K)]
  0x00000287ec0fb0f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=169292, stack(0x0000009ec2500000,0x0000009ec2600000) (1024K)]
  0x00000287ec0feb60 JavaThread "Service Thread"             daemon [_thread_blocked, id=169296, stack(0x0000009ec2600000,0x0000009ec2700000) (1024K)]
  0x00000287ec1019d0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=169300, stack(0x0000009ec2700000,0x0000009ec2800000) (1024K)]
=>0x00000287ec102750 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=169304, stack(0x0000009ec2800000,0x0000009ec2900000) (1024K)]
  0x00000287ec10d200 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=169308, stack(0x0000009ec2900000,0x0000009ec2a00000) (1024K)]
  0x00000287ec25df60 JavaThread "Notification Thread"        daemon [_thread_blocked, id=169312, stack(0x0000009ec2a00000,0x0000009ec2b00000) (1024K)]
  0x00000287ec264200 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=169316, stack(0x0000009ec2b00000,0x0000009ec2c00000) (1024K)]
  0x00000287ec654950 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=169324, stack(0x0000009ec2d00000,0x0000009ec2e00000) (1024K)]
  0x00000287ec90a380 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=169332, stack(0x0000009ec2f00000,0x0000009ec3000000) (1024K)]
  0x00000287ec60bb70 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=169336, stack(0x0000009ec3000000,0x0000009ec3100000) (1024K)]
  0x00000287ec8c9a10 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=169340, stack(0x0000009ec3100000,0x0000009ec3200000) (1024K)]
  0x00000287ec862350 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=169344, stack(0x0000009ec3200000,0x0000009ec3300000) (1024K)]
Total: 16

Other Threads:
  0x000002878a685380 VMThread "VM Thread"                           [id=169276, stack(0x0000009ec2100000,0x0000009ec2200000) (1024K)]
  0x000002878a677b30 WatcherThread "VM Periodic Task Thread"        [id=169272, stack(0x0000009ec2000000,0x0000009ec2100000) (1024K)]
  0x000002878a52cb30 WorkerThread "GC Thread#0"                     [id=169252, stack(0x0000009ec1b00000,0x0000009ec1c00000) (1024K)]
  0x000002878a53e800 ConcurrentGCThread "G1 Main Marker"            [id=169256, stack(0x0000009ec1c00000,0x0000009ec1d00000) (1024K)]
  0x000002878a53f3c0 WorkerThread "G1 Conc#0"                       [id=169260, stack(0x0000009ec1d00000,0x0000009ec1e00000) (1024K)]
  0x000002878a629fd0 ConcurrentGCThread "G1 Refine#0"               [id=169264, stack(0x0000009ec1e00000,0x0000009ec1f00000) (1024K)]
  0x000002878a62aa50 ConcurrentGCThread "G1 Service"                [id=169268, stack(0x0000009ec1f00000,0x0000009ec2000000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  470 1063       4       sun.security.ec.ECOperations::setDouble (463 bytes)
C2 CompilerThread2  470 1080       4       jdk.internal.org.objectweb.asm.SymbolTable::addConstantUtf8 (98 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9b526c830] Metaspace_lock - owner thread: 0x00000287ec90a380

Heap address: 0x0000000604400000, size: 8124 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000287ab000000-0x00000287abd00000-0x00000287abd00000), size 13631488, SharedBaseAddress: 0x00000287ab000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000287ac000000-0x00000287ec000000, reserved size: 1073741824
Narrow klass base: 0x00000287ab000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32490M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 8124M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 520192K, used 12288K [0x0000000604400000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 8854K, committed 9088K, reserved 1114112K
  class space    used 1018K, committed 1152K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   1|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   2|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   3|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   4|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   5|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   6|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   7|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|   8|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|   9|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  10|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  11|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  12|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  13|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  14|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  15|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  16|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  17|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  18|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  19|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  20|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  21|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  22|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  23|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  24|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  25|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  26|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  27|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  28|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  29|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  30|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  31|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  32|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  33|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  34|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  35|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  36|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  37|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  38|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  39|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  40|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  41|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  42|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  43|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  44|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  45|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  46|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  47|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  48|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  49|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  50|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  51|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  52|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  53|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  54|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  55|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  56|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  57|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  58|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  59|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  60|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  61|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  62|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  63|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  64|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  65|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  66|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  67|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  68|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  69|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  70|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  71|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  72|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  73|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  74|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  75|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  76|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  77|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  78|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  79|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  80|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  81|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  82|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  83|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  84|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  85|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  86|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  87|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  88|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  89|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  90|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  91|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  92|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  93|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  94|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  95|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  96|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  97|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
|  98|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
|  99|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 100|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 101|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 102|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 103|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 104|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 105|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 106|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 107|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 108|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 109|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 110|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 111|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 112|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 113|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 114|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 115|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 116|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 117|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 118|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 119|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 120|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 121|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 122|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 123|0x0000000623000000, 0x00000006233da250, 0x0000000623400000| 96%| E|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 124|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 125|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 
| 126|0x0000000623c00000, 0x0000000624000000, 0x0000000624000000|100%| E|CS|TAMS 0x0000000623c00000| PB 0x0000000623c00000| Complete 

Card table byte_map: [0x000002879f3b0000,0x00000287a0390000] _byte_map_base: 0x000002879c38e000

Marking Bits: (CMBitMap*) 0x000002878a52e150
 Bits: [0x00000287a0390000, 0x00000287a8280000)

Polling page: 0x00000287898e0000

Metaspace:

Usage:
  Non-class:      7.65 MB used.
      Class:   1018.93 KB used.
       Both:      8.65 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.75 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.12 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.88 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.12 MB
       Class:  14.73 MB
        Both:  22.86 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 230.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 142.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 384.
num_chunk_merges: 0.
num_chunk_splits: 222.
num_chunks_enlarged: 100.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=395Kb max_used=395Kb free=119604Kb
 bounds [0x0000028796730000, 0x00000287969a0000, 0x000002879dc60000]
CodeHeap 'profiled nmethods': size=120000Kb used=1757Kb max_used=1757Kb free=118242Kb
 bounds [0x000002878ec60000, 0x000002878eed0000, 0x0000028796190000]
CodeHeap 'non-nmethods': size=5760Kb used=1585Kb max_used=1599Kb free=4174Kb
 bounds [0x0000028796190000, 0x0000028796400000, 0x0000028796730000]
 total_blobs=1635 nmethods=1116 adapters=424
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.409 Thread 0x00000287ec10d200 nmethod 1078 0x000002878edf7c10 code [0x000002878edf7dc0, 0x000002878edf7f08]
Event: 0.409 Thread 0x00000287ec862350 nmethod 1079 0x000002879678eb10 code [0x000002879678eca0, 0x000002879678ee10]
Event: 0.409 Thread 0x00000287ec862350 1080       4       jdk.internal.org.objectweb.asm.SymbolTable::addConstantUtf8 (98 bytes)
Event: 0.410 Thread 0x00000287ec10d200 1081       1       java.security.Provider::getName (5 bytes)
Event: 0.410 Thread 0x00000287ec10d200 nmethod 1081 0x000002879678ef10 code [0x000002879678f0a0, 0x000002879678f170]
Event: 0.412 Thread 0x00000287ec10d200 1083       3       java.lang.Boolean::valueOf (14 bytes)
Event: 0.412 Thread 0x00000287ec10d200 nmethod 1083 0x000002878edf7f90 code [0x000002878edf8140, 0x000002878edf82a8]
Event: 0.413 Thread 0x00000287ec10d200 1084       3       java.lang.invoke.MethodHandles$Lookup::resolveOrFail (48 bytes)
Event: 0.413 Thread 0x00000287ec10d200 nmethod 1084 0x000002878edf8310 code [0x000002878edf8560, 0x000002878edf8c18]
Event: 0.413 Thread 0x00000287ec10d200 1085       3       java.lang.invoke.MethodHandles$Lookup::checkMethodName (45 bytes)
Event: 0.413 Thread 0x00000287ec10d200 nmethod 1085 0x000002878edf8f10 code [0x000002878edf9160, 0x000002878edf9828]
Event: 0.413 Thread 0x00000287ec10d200 1086       3       jdk.internal.ref.PhantomCleanable::<init> (49 bytes)
Event: 0.414 Thread 0x00000287ec10d200 nmethod 1086 0x000002878edf9b10 code [0x000002878edf9d80, 0x000002878edfa8c0]
Event: 0.414 Thread 0x00000287ec10d200 1087       3       sun.security.ec.XECOperations::bitAt (23 bytes)
Event: 0.414 Thread 0x00000287ec10d200 nmethod 1087 0x000002878edfac10 code [0x000002878edfadc0, 0x000002878edfaf58]
Event: 0.414 Thread 0x00000287ec10d200 1088       3       sun.security.util.math.intpoly.IntegerPolynomial25519::reduce (40 bytes)
Event: 0.414 Thread 0x00000287ec10d200 nmethod 1088 0x000002878edfb090 code [0x000002878edfb260, 0x000002878edfb560]
Event: 0.414 Thread 0x00000287ec10d200 1091       3       sun.security.util.math.intpoly.IntegerPolynomial25519::carryReduce (391 bytes)
Event: 0.414 Thread 0x00000287ec10d200 nmethod 1091 0x000002878edfb710 code [0x000002878edfb8c0, 0x000002878edfbd20]
Event: 0.414 Thread 0x00000287ec10d200 1092       3       java.lang.invoke.MethodHandleNatives::canBeCalledVirtual (49 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.010 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\java.dll
Event: 0.032 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000028796789a40 relative=0x0000000000000260
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000028796789a40 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.414 Thread 0x00000287ec90a380 DEOPT PACKING pc=0x0000028796789a40 sp=0x0000009ec2ffe760
Event: 0.414 Thread 0x00000287ec90a380 DEOPT UNPACKING pc=0x00000287961e7da2 sp=0x0000009ec2ffe6a0 mode 2
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028796784d84 relative=0x00000000000001c4
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028796784d84 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.414 Thread 0x00000287ec90a380 DEOPT PACKING pc=0x0000028796784d84 sp=0x0000009ec2ffe6d0
Event: 0.414 Thread 0x00000287ec90a380 DEOPT UNPACKING pc=0x00000287961e7da2 sp=0x0000009ec2ffe6a0 mode 2
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028796784d84 relative=0x00000000000001c4
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028796784d84 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.414 Thread 0x00000287ec90a380 DEOPT PACKING pc=0x0000028796784d84 sp=0x0000009ec2ffe6d0
Event: 0.414 Thread 0x00000287ec90a380 DEOPT UNPACKING pc=0x00000287961e7da2 sp=0x0000009ec2ffe6a0 mode 2
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028796784d84 relative=0x00000000000001c4
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028796784d84 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.414 Thread 0x00000287ec90a380 DEOPT PACKING pc=0x0000028796784d84 sp=0x0000009ec2ffe6d0
Event: 0.414 Thread 0x00000287ec90a380 DEOPT UNPACKING pc=0x00000287961e7da2 sp=0x0000009ec2ffe6a0 mode 2
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000028796784d84 relative=0x00000000000001c4
Event: 0.414 Thread 0x00000287ec90a380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000028796784d84 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.414 Thread 0x00000287ec90a380 DEOPT PACKING pc=0x0000028796784d84 sp=0x0000009ec2ffe6d0
Event: 0.414 Thread 0x00000287ec90a380 DEOPT UNPACKING pc=0x00000287961e7da2 sp=0x0000009ec2ffe6a0 mode 2

Classes loaded (20 events):
Event: 0.410 Loading class java/security/spec/X509EncodedKeySpec
Event: 0.410 Loading class java/security/spec/EncodedKeySpec
Event: 0.410 Loading class java/security/spec/EncodedKeySpec done
Event: 0.410 Loading class java/security/spec/X509EncodedKeySpec done
Event: 0.411 Loading class sun/security/ssl/XDHKeyExchange
Event: 0.411 Loading class sun/security/ssl/XDHKeyExchange done
Event: 0.411 Loading class sun/security/ssl/XDHKeyExchange$XDHEKAGenerator
Event: 0.411 Loading class sun/security/ssl/XDHKeyExchange$XDHEKAGenerator done
Event: 0.412 Loading class sun/security/ssl/KAKeyDerivation
Event: 0.412 Loading class sun/security/ssl/KAKeyDerivation done
Event: 0.412 Loading class javax/crypto/KeyAgreementSpi
Event: 0.412 Loading class javax/crypto/KeyAgreementSpi done
Event: 0.412 Loading class javax/crypto/ShortBufferException
Event: 0.412 Loading class javax/crypto/ShortBufferException done
Event: 0.412 Loading class javax/crypto/SecretKey
Event: 0.412 Loading class javax/crypto/SecretKey done
Event: 0.414 Loading class javax/crypto/spec/SecretKeySpec
Event: 0.414 Loading class javax/crypto/spec/SecretKeySpec done
Event: 0.414 Loading class jdk/internal/access/JavaxCryptoSpecAccess
Event: 0.414 Loading class jdk/internal/access/JavaxCryptoSpecAccess done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.235 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b2dcb0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623b2dcb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.235 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b34748}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000623b34748) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.235 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b3b1d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623b3b1d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.236 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b3f860}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623b3f860) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.238 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b693d8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b693d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.239 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b6cd68}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000623b6cd68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.240 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623b7c2d0}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623b7c2d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.242 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bae098}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000623bae098) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.243 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bb4968}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bb4968) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.243 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623bb7d88}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623bb7d88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.294 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623554a70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623554a70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.311 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623758820}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000623758820) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.311 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062375c188}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000062375c188) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.316 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062378a6d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062378a6d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.325 Thread 0x00000287ec654950 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623593608}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623593608) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.326 Thread 0x000002878804f0b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062301a278}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062301a278) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.329 Thread 0x00000287ec654950 Exception <a 'java/lang/NoSuchMethodError'{0x00000006235cd6c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000006235cd6c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.331 Thread 0x00000287ec90a380 Exception <a 'java/lang/NoSuchMethodError'{0x000000062309efc0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062309efc0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.336 Thread 0x00000287ec90a380 Exception <a 'java/lang/NoSuchMethodError'{0x00000006230f0e78}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006230f0e78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.408 Thread 0x00000287ec90a380 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623308448}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000623308448) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (10 events):
Event: 0.091 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.091 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.095 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.095 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.200 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.200 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.326 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.326 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.412 Executing VM operation: ICBufferFull
Event: 0.412 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec0ef7a0
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec0f8590
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec0fb0f0
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec0feb60
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec1019d0
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec102750
Event: 0.039 Thread 0x000002878804f0b0 Thread added: 0x00000287ec10d200
Event: 0.049 Thread 0x000002878804f0b0 Thread added: 0x00000287ec25df60
Event: 0.050 Thread 0x000002878804f0b0 Thread added: 0x00000287ec264200
Event: 0.053 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\net.dll
Event: 0.086 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\nio.dll
Event: 0.088 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\zip.dll
Event: 0.122 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\jimage.dll
Event: 0.134 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\sunmscapi.dll
Event: 0.285 Loaded shared library E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\extnet.dll
Event: 0.291 Thread 0x000002878804f0b0 Thread added: 0x00000287ec654950
Event: 0.329 Thread 0x00000287ec654950 Thread added: 0x00000287ec90a380
Event: 0.340 Thread 0x00000287ec654950 Thread added: 0x00000287ec60bb70
Event: 0.370 Thread 0x00000287ec10d200 Thread added: 0x00000287ec8c9a10
Event: 0.371 Thread 0x00000287ec102750 Thread added: 0x00000287ec862350


Dynamic libraries:
0x00007ff778cf0000 - 0x00007ff778cfa000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\java.exe
0x00007ffa68a30000 - 0x00007ffa68c28000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa66a50000 - 0x00007ffa66b12000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa66230000 - 0x00007ffa6652e000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5df00000 - 0x00007ffa5df94000 	C:\Windows\SYSTEM32\apphelp.dll
0x0000000064a80000 - 0x0000000064b15000 	C:\Windows\System32\SYSFER.DLL
0x00007ffa66f90000 - 0x00007ffa67041000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa66ef0000 - 0x00007ffa66f8e000 	C:\Windows\System32\msvcrt.dll
0x00007ffa67b50000 - 0x00007ffa67bef000 	C:\Windows\System32\sechost.dll
0x00007ffa67050000 - 0x00007ffa67173000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa66a20000 - 0x00007ffa66a47000 	C:\Windows\System32\bcrypt.dll
0x00007ffa66650000 - 0x00007ffa66750000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa5ac70000 - 0x00007ffa5ac88000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\jli.dll
0x00007ffa5cff0000 - 0x00007ffa5d00b000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\VCRUNTIME140.dll
0x00007ffa66d50000 - 0x00007ffa66eed000 	C:\Windows\System32\USER32.dll
0x00007ffa66160000 - 0x00007ffa66182000 	C:\Windows\System32\win32u.dll
0x00007ffa67c60000 - 0x00007ffa67c8b000 	C:\Windows\System32\GDI32.dll
0x00007ffa66530000 - 0x00007ffa66647000 	C:\Windows\System32\gdi32full.dll
0x00007ffa66190000 - 0x00007ffa6622d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa4b460000 - 0x00007ffa4b6fa000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffa66c30000 - 0x00007ffa66c5f000 	C:\Windows\System32\IMM32.DLL
0x0000000064a40000 - 0x0000000064a4d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffa476a0000 - 0x00007ffa477a3000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffa67180000 - 0x00007ffa678ed000 	C:\Windows\System32\SHELL32.dll
0x00007ffa67bf0000 - 0x00007ffa67c45000 	C:\Windows\System32\SHLWAPI.dll
0x00007ffa65e00000 - 0x00007ffa65e0a000 	C:\Windows\SYSTEM32\VERSION.dll
0x0000000180000000 - 0x000000018009c000 	C:\Windows\LVUAAgentInstBaseRoot\system32\Vozokopot.dll
0x00007ffa2a240000 - 0x00007ffa2a2a3000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathWeb.dll
0x00007ffa39130000 - 0x00007ffa396b2000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathCore.dll
0x00007ffa66c60000 - 0x00007ffa66d3a000 	C:\Windows\System32\COMDLG32.dll
0x00007ffa68630000 - 0x00007ffa68983000 	C:\Windows\System32\combase.dll
0x00007ffa67c90000 - 0x00007ffa67d3d000 	C:\Windows\System32\shcore.dll
0x00007ffa678f0000 - 0x00007ffa67a1b000 	C:\Windows\System32\ole32.dll
0x00007ffa68560000 - 0x00007ffa6862d000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa67e70000 - 0x00007ffa67edb000 	C:\Windows\System32\WS2_32.dll
0x0000000063430000 - 0x00000000634ca000 	C:\Windows\LVUAAgentInstBaseRoot\system32\SteinwayMSVCRT.dll
0x0000000063360000 - 0x0000000063430000 	C:\Windows\LVUAAgentInstBaseRoot\system32\SteinwayMSVCSTL.dll
0x00007ffa4a520000 - 0x00007ffa4a5c4000 	C:\Windows\SYSTEM32\WINSPOOL.DRV
0x00007ffa5d380000 - 0x00007ffa5d394000 	C:\Windows\SYSTEM32\WTSAPI32.dll
0x00007ffa65480000 - 0x00007ffa654bb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa654d0000 - 0x00007ffa6559a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa48530000 - 0x00007ffa4854d000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ffa67e60000 - 0x00007ffa67e68000 	C:\Windows\System32\NSI.dll
0x00007ffa360c0000 - 0x00007ffa360e1000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathFw.dll
0x00007ffa36040000 - 0x00007ffa3607a000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathNet.dll
0x00007ffa346e0000 - 0x00007ffa34711000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathProcess.dll
0x00007ffa348c0000 - 0x00007ffa348da000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathBolo.dll
0x00007ffa348e0000 - 0x00007ffa348f5000 	C:\Windows\LVUAAgentInstBaseRoot\system32\MozartBreathProtect.dll
0x00007ffa59450000 - 0x00007ffa5945c000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\vcruntime140_1.dll
0x00007ffa34070000 - 0x00007ffa340fd000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\msvcp140.dll
0x00007ff9b45a0000 - 0x00007ff9b5361000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\server\jvm.dll
0x00007ffa655a0000 - 0x00007ffa655eb000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa544e0000 - 0x00007ffa54507000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa65460000 - 0x00007ffa65472000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa64950000 - 0x00007ffa64962000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa59440000 - 0x00007ffa5944a000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\jimage.dll
0x00007ffa64200000 - 0x00007ffa643e4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa48130000 - 0x00007ffa48164000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa660d0000 - 0x00007ffa66152000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa67c50000 - 0x00007ffa67c58000 	C:\Windows\System32\psapi.dll
0x00007ffa5b0f0000 - 0x00007ffa5b164000 	C:\Windows\SYSTEM32\Wlanapi.dll
0x00007ffa66000000 - 0x00007ffa66024000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa59420000 - 0x00007ffa59440000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\java.dll
0x00007ffa63980000 - 0x00007ffa64124000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa65a90000 - 0x00007ffa65abe000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffa37c10000 - 0x00007ffa37c28000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\zip.dll
0x00007ffa37c00000 - 0x00007ffa37c10000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\net.dll
0x00007ffa5d5b0000 - 0x00007ffa5d6bd000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa4b040000 - 0x00007ffa4b321000 	C:\Windows\Netease\Booster\lsp64.dll
0x00007ffa66750000 - 0x00007ffa668ac000 	C:\Windows\System32\CRYPT32.dll
0x00007ffa657f0000 - 0x00007ffa6585a000 	C:\Windows\system32\mswsock.dll
0x00007ffa66900000 - 0x00007ffa66969000 	C:\Windows\System32\WINTRUST.DLL
0x00007ffa65c20000 - 0x00007ffa65c32000 	C:\Windows\SYSTEM32\MSASN1.dll
0x00007ffa67a20000 - 0x00007ffa67a3d000 	C:\Windows\System32\imagehlp.dll
0x00007ffa659e0000 - 0x00007ffa659f8000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa650a0000 - 0x00007ffa650d4000 	C:\Windows\system32\rsaenh.dll
0x00007ffa65a00000 - 0x00007ffa65a0c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ff9b5ff0000 - 0x00007ff9b6006000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\nio.dll
0x00007ff9b4360000 - 0x00007ff9b436e000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\sunmscapi.dll
0x00007ffa65b00000 - 0x00007ffa65b27000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffa65ac0000 - 0x00007ffa65afb000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffa65fc0000 - 0x00007ffa65fee000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ff9b4350000 - 0x00007ff9b4357000 	C:\Windows\system32\wshunix.dll
0x00007ff9b4340000 - 0x00007ff9b4349000 	E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Program Files (x86)\360\360Safe\safemon;C:\Windows\LVUAAgentInstBaseRoot\system32;E:\dev\idea\IntelliJ IDEA 2024.1\jbr\bin\server;C:\Windows\Netease\Booster

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://************:8081': 
java_class_path (initial): E:/dev/idea/IntelliJ IDEA 2024.1/plugins/vcs-git/lib/git4idea-rt.jar;E:/dev/idea/IntelliJ IDEA 2024.1/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8518631424                                {product} {ergonomic}
   size_t MaxNewSize                               = 5108662272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8518631424                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=E:\dev\MyProject\SiriusPro\jdk
PATH=E:/dev/GIT/Git/mingw64/libexec/git-core;E:/dev/GIT/Git/mingw64/libexec/git-core;E:\dev\GIT\Git\mingw64\bin;E:\dev\GIT\Git\usr\bin;C:\Users\<USER>\bin;E:\dev\VMWeare16\bin\;C:\Program Files\Common Files\Oracle\Java\javapath;E:\dev\MyProject\SiriusPro\jdk\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;E:\dev\SVN\bin;E:\dev\nodeJs\;E:\dev\weChatDev\΢��web�����߹���\dll;E:\dev;UltraEdit;E:\dev\maven\apache-maven-3.8.8\bin;E:\dev\GIT\bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;E:\dev\GIT\Git\cmd;E:\dev\ffmpeg\ffmpeg-master-latest-win64-gpl\bin;E:\dev\Python\Scripts\;E:\dev\Python\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\program files\esafenet\cobra docguard client;E:\dev\idea\IntelliJ IDEA 2024.1\bin;C:\Users\<USER>\AppData\Roaming\npm
USERNAME=fangzhen
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 93300K (0% of 33270564K total physical memory with 1250232K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
