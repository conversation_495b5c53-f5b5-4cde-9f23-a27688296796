package com.yzt.module.salesorder.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;


public interface ErrorCodeConstants {

    ErrorCode PARTS_NOT_EXISTS = new ErrorCode(21000001, "订单要求部件不存在");

    ErrorCode FILL_TYPE_NOT_EXISTS = new ErrorCode(21000002, "填充类型不存在");

    ErrorCode TEST_GRP_NOT_EXISTS = new ErrorCode(21000003, "分组管理不存在");

    ErrorCode TEST_GRP_EXITS_CHILDREN = new ErrorCode(21000004, "存在存在子分组管理，无法删除");

    ErrorCode TEST_GRP_PARENT_NOT_EXITS = new ErrorCode(21000005,"父级分组管理不存在");

    ErrorCode TEST_GRP_PARENT_ERROR = new ErrorCode(21000006, "不能设置自己为父分组管理");

    ErrorCode TEST_GRP_TESTGRPNAME_DUPLICATE = new ErrorCode(21000007, "已经存在该模块名称的分组管理");

    ErrorCode TEST_GRP_PARENT_IS_CHILD = new ErrorCode(21000008, "不能设置自己的子TestGrp为父TestGrp");

    ErrorCode FIELDS_NOT_EXISTS = new ErrorCode(21000009, "字段管理不存在");

    ErrorCode TEST_ITEMS_NOT_EXISTS = new ErrorCode(21000010, "检验子项不存在");

    ErrorCode SELECTS_NOT_EXISTS = new ErrorCode(21000011, "下拉框选项管理不存在");

    ErrorCode TEST_RELATION_NOT_EXISTS = new ErrorCode(21000011, "字段-部件-子项关联表不存在");

    ErrorCode TEST_GRP_PARTS_NOT_EXISTS = new ErrorCode(21000012, "模块部件不存在");

    ErrorCode ORDER_MAIN_NOT_EXISTS = new ErrorCode(21000013, "订单主数据不存在");

    ErrorCode ORDER_DETAIL_NOT_EXISTS = new ErrorCode(21000014, "订单明细数据不存在");

    ErrorCode ORDER_DETAIL_RN_NOT_EXISTS = new ErrorCode(21000015, "订单明细数据行号不存在");

    ErrorCode SUMMARY_FIELDS_NOT_EXISTS = new ErrorCode(21000016, "汇总数据字段模板不存在");

    ErrorCode CUSTOMER_INFO_NOT_EXISTS = new ErrorCode(21000017, "客户信息不存在");

    ErrorCode CUSTOMER_INFO_WF_NOT_EXISTS = new ErrorCode(21000018, "客户信息流程不存在");

    ErrorCode ORDER_MAIN_WF_NOT_EXISTS = new ErrorCode(21000019, "合同评审流程不存在");

    ErrorCode PART_INFO_NOT_EXISTS = new ErrorCode(21000020, "部件代码不存在");

    ErrorCode PART_CLASS_NOT_EXISTS = new ErrorCode(21000021, "部件分类不存在");

    ErrorCode CUSTOMER_PRJ_WF_NOT_EXISTS = new ErrorCode(21000022, "客户项目管理流程不存在");

    ErrorCode CUSTOMER_PRJ_NOT_EXISTS = new ErrorCode(21000023, "客户项目名称不存在");

    ErrorCode COMMISSION_INFO_NOT_EXISTS = new ErrorCode(21000024, "佣金汇总不存在");

    ErrorCode COMMISSION_INFO_DT1_NOT_EXISTS = new ErrorCode(21000025, "佣金汇总明细不存在");

    ErrorCode QUOTATION_INFO_NOT_EXISTS = new ErrorCode(21000025, "报价单号不存在");

    ErrorCode ORDER_SUMMARY_WF_NOT_EXISTS = new ErrorCode(21000026, "合同评审流程汇总数据不存在");

    ErrorCode ORDER_PARTS_SUMMARY_WF_NOT_EXISTS = new ErrorCode(21000027, "合同评审流程汇总部件数据不存在");

    ErrorCode ORDER_SUMMARY_NOT_EXISTS = new ErrorCode(21000028, "合同评审汇总数据不存在");

    ErrorCode ORDER_PARTS_SUMMARY_NOT_EXISTS = new ErrorCode(21000029, "合同评审汇总部件数据不存在");

    ErrorCode ORDER_PARTS_SUMMARY_CHANGE_WF_NOT_EXISTS = new ErrorCode(21000030, "合同评审变更流程部件差异数据不存在");

    ErrorCode RECV_TERM_NOT_EXISTS = new ErrorCode(21000040, "收款条件不存在");

    ErrorCode CATEGORY_NOT_EXISTS = new ErrorCode(21000050, "销售单别不存在");

}
