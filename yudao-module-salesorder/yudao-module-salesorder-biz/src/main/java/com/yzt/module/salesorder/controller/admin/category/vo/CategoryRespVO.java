package com.yzt.module.salesorder.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 销售单别 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CategoryRespVO {

    @Schema(description = "自增长id", requiredMode = Schema.RequiredMode.REQUIRED, example = "535")
    @ExcelProperty("自增长id")
    private Long id;

    @Schema(description = "销售单别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("销售单别代码")
    private String code;

    @Schema(description = "销售单别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("销售单别名称")
    private String name;

}