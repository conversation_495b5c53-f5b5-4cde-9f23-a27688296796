package com.yzt.module.salesorder.controller.admin.ordermainwf.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合同评审流程分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderMainWfPageReqVO extends PageParam {

    @Schema(description = "销售订单号")
    private String salesorder;

    @Schema(description = "提交人")
    private Integer submitter;

    @Schema(description = "部门")
    private Integer department;

    @Schema(description = "提交日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] submitdate;

    @Schema(description = "客户名称", example = "赵六")
    private String customername;

    @Schema(description = "客户代码")
    private String customercode;

    @Schema(description = "客户订单号")
    private String ctmorderno;

    @Schema(description = "报价单号")
    private String quoteno;

    @Schema(description = "项目编号")
    private String prjno;

    @Schema(description = "项目名称", example = "王五")
    private String prjname;

    private Integer mid;

    @Schema(description = "是否需要线下评审")
    private Integer offlineReview;

    @Schema(description = "商标")
    private String brand;

    @Schema(description = "收款条件代码")
    private String recvCode;

    @Schema(description = "收款条件名称")
    private String recvName;

    @Schema(description = "汇率")
    private BigDecimal exRate;

    @Schema(description = "销售订单性质")
    private String soNature;

    @Schema(description = "销售订单类型")
    private String soType;
}