package com.yzt.module.salesorder.controller.admin.category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 销售单别新增/修改 Request VO")
@Data
public class CategorySaveReqVO {

    @Schema(description = "自增长id", requiredMode = Schema.RequiredMode.REQUIRED, example = "535")
    private Long id;

    @Schema(description = "销售单别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "销售单别代码不能为空")
    private String code;

    @Schema(description = "销售单别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "销售单别名称不能为空")
    private String name;

}