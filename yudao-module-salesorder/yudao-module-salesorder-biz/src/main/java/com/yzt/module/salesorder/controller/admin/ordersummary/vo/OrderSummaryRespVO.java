package com.yzt.module.salesorder.controller.admin.ordersummary.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 合同评审汇总数据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderSummaryRespVO {

    @Schema(description = "自增长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20274")
    @ExcelProperty("自增长ID")
    private Integer id;

    @Schema(description = "主数据ID", example = "14712")
    @ExcelProperty("主数据ID")
    private String mainid;

    @Schema(description = "行号")
    @ExcelProperty("行号")
    private String hh;

    @Schema(description = "Item")
    @ExcelProperty("Item")
    private String item;

    @Schema(description = "品号")
    @ExcelProperty("品号")
    private String ph;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String cpmc;

    @Schema(description = "规格型号")
    @ExcelProperty("规格型号")
    private String ggxh;

    @Schema(description = "主体")
    @ExcelProperty("主体")
    private String zt;

    @Schema(description = "阀芯")
    @ExcelProperty("阀芯")
    private String fx;

    @Schema(description = "阀杆")
    @ExcelProperty("阀杆")
    private String fg;

    @Schema(description = "阀座")
    @ExcelProperty("阀座")
    private String fz;

    @Schema(description = "密封面")
    @ExcelProperty("密封面")
    private String mfm;

    @Schema(description = "设计标准")
    @ExcelProperty("设计标准")
    private String sjbz;

    @Schema(description = "连接标准")
    @ExcelProperty("连接标准")
    private String ljbz;

    @Schema(description = "结构长度")
    @ExcelProperty("结构长度")
    private String jgcd;

    @Schema(description = "位号")
    @ExcelProperty("位号")
    private String wh;

    @Schema(description = "数量")
    @ExcelProperty("数量")
    private Integer sl;

    @Schema(description = "单价")
    @ExcelProperty("单价")
    private BigDecimal dj;

    @Schema(description = "小计")
    @ExcelProperty("小计")
    private BigDecimal xj;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String bz;

    @Schema(description = "货物名称")
    @ExcelProperty("货物名称")
    private String hwmc;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "图号标准号")
    @ExcelProperty("图号标准号")
    private String bwgNormNo;

    @Schema(description = "规格尺寸")
    @ExcelProperty("规格尺寸")
    private String specSize;

    @Schema(description = "客户产品型号")
    @ExcelProperty("客户产品型号")
    private String customerPml;

    @Schema(description = "交货日期")
    @ExcelProperty("交货日期")
    private LocalDate deliveryDate;

    @Schema(description = "销售单位")
    @ExcelProperty("销售单位")
    private String salesUnit;

    @Schema(description = "含税单价")
    @ExcelProperty("含税单价")
    private BigDecimal taxPrice;

    @Schema(description = "税前金额")
    @ExcelProperty("税前金额")
    private BigDecimal notaxAmount;

    @Schema(description = "含税金额")
    @ExcelProperty("含税金额")
    private BigDecimal taxAmount;

    @Schema(description = "代理价1价格")
    @ExcelProperty("代理价1价格")
    private BigDecimal aponePrice;

    @Schema(description = "代理价1金额")
    @ExcelProperty("代理价1金额")
    private BigDecimal aponeAmount;

    @Schema(description = "代理价2价格")
    @ExcelProperty("代理价2价格")
    private BigDecimal aptwoPrice;

    @Schema(description = "代理价2金额")
    @ExcelProperty("代理价2金额")
    private BigDecimal aptwoAmount;

    @Schema(description = "商标代码")
    @ExcelProperty("商标代码")
    private String trademarkCode;

    @Schema(description = "所属运营中心")
    @ExcelProperty("所属运营中心")
    private String opcen;

    @Schema(description = "所属法人")
    @ExcelProperty("所属法人")
    private String legalEntity;

}