package com.yzt.module.salesorder.dal.mysql.ordersummary;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yzt.module.salesorder.controller.admin.ordersummary.vo.OrderSummaryPageReqVO;
import com.yzt.module.salesorder.dal.dataobject.ordersummary.OrderSummaryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合同评审汇总数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderSummaryMapper extends BaseMapperX<OrderSummaryDO> {

    default PageResult<OrderSummaryDO> selectPage(OrderSummaryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderSummaryDO>()
                .eqIfPresent(OrderSummaryDO::getMainid, reqVO.getMainid())
                .eqIfPresent(OrderSummaryDO::getItem, reqVO.getItem())
                .eqIfPresent(OrderSummaryDO::getPh, reqVO.getPh())
                .eqIfPresent(OrderSummaryDO::getCpmc, reqVO.getCpmc())
                .eqIfPresent(OrderSummaryDO::getGgxh, reqVO.getGgxh())
                .eqIfPresent(OrderSummaryDO::getZt, reqVO.getZt())
                .eqIfPresent(OrderSummaryDO::getFx, reqVO.getFx())
                .eqIfPresent(OrderSummaryDO::getFg, reqVO.getFg())
                .eqIfPresent(OrderSummaryDO::getFz, reqVO.getFz())
                .eqIfPresent(OrderSummaryDO::getMfm, reqVO.getMfm())
                .eqIfPresent(OrderSummaryDO::getSjbz, reqVO.getSjbz())
                .eqIfPresent(OrderSummaryDO::getLjbz, reqVO.getLjbz())
                .eqIfPresent(OrderSummaryDO::getJgcd, reqVO.getJgcd())
                .eqIfPresent(OrderSummaryDO::getWh, reqVO.getWh())
                .eqIfPresent(OrderSummaryDO::getSl, reqVO.getSl())
                .eqIfPresent(OrderSummaryDO::getDj, reqVO.getDj())
                .eqIfPresent(OrderSummaryDO::getXj, reqVO.getXj())
                .eqIfPresent(OrderSummaryDO::getBz, reqVO.getBz())
                .eqIfPresent(OrderSummaryDO::getHwmc, reqVO.getHwmc())
                .betweenIfPresent(OrderSummaryDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OrderSummaryDO::getBwgNormNo, reqVO.getBwgNormNo())
                .eqIfPresent(OrderSummaryDO::getSpecSize, reqVO.getSpecSize())
                .eqIfPresent(OrderSummaryDO::getCustomerPml, reqVO.getCustomerPml())
                .eqIfPresent(OrderSummaryDO::getSalesUnit, reqVO.getSalesUnit())
                .eqIfPresent(OrderSummaryDO::getTaxPrice, reqVO.getTaxPrice())
                .eqIfPresent(OrderSummaryDO::getNotaxAmount, reqVO.getNotaxAmount())
                .eqIfPresent(OrderSummaryDO::getTaxAmount, reqVO.getTaxAmount())
                .eqIfPresent(OrderSummaryDO::getAponePrice, reqVO.getAponePrice())
                .eqIfPresent(OrderSummaryDO::getAponeAmount, reqVO.getAponeAmount())
                .eqIfPresent(OrderSummaryDO::getAptwoPrice, reqVO.getAptwoPrice())
                .eqIfPresent(OrderSummaryDO::getAptwoAmount, reqVO.getAptwoAmount())
                .eqIfPresent(OrderSummaryDO::getTrademarkCode, reqVO.getTrademarkCode())
                .eqIfPresent(OrderSummaryDO::getOpcen, reqVO.getOpcen())
                .eqIfPresent(OrderSummaryDO::getLegalEntity, reqVO.getLegalEntity())
                .betweenIfPresent(OrderSummaryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(OrderSummaryDO::getId));
    }

    void deleteByMainid(@Param("mainid")String mainid);

    void insertOrderSummary(@Param("orderMainWfId")String orderMainWfId, @Param("mainid")String mainid);
}