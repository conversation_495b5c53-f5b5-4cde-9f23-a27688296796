package com.yzt.module.salesorder.controller.admin.ordersummarywf.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 合同评审流程汇总数据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderSummaryWfPageReqVO extends PageParam {

    @Schema(description = "主数据ID", example = "30783")
    private String mainid;

    @Schema(description = "行号")
    private String hh;

    @Schema(description = "Item")
    private String item;

    @Schema(description = "产品名称")
    private String cpmc;

    @Schema(description = "规格型号")
    private String ggxh;

    @Schema(description = "主体")
    private String zt;

    @Schema(description = "阀芯")
    private String fx;

    @Schema(description = "阀杆")
    private String fg;

    @Schema(description = "阀座")
    private String fz;

    @Schema(description = "密封面")
    private String mfm;

    @Schema(description = "设计标准")
    private String sjbz;

    @Schema(description = "连接标准")
    private String ljbz;

    @Schema(description = "结构长度")
    private String jgcd;

    @Schema(description = "位号")
    private String wh;

    @Schema(description = "数量")
    private Integer sl;

    @Schema(description = "单价")
    private BigDecimal dj;

    @Schema(description = "小计")
    private BigDecimal xj;

    @Schema(description = "备注")
    private String bz;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "图号标准号")
    private String bwgNormNo;

    @Schema(description = "规格尺寸")
    private String specSize;

    @Schema(description = "客户产品型号")
    private String customerPml;

    @Schema(description = "交货日期")
    private LocalDate deliveryDate;

    @Schema(description = "销售单位")
    private String salesUnit;

    @Schema(description = "含税单价")
    private BigDecimal taxPrice;

    @Schema(description = "税前金额")
    private BigDecimal notaxAmount;

    @Schema(description = "含税金额")
    private BigDecimal taxAmount;

    @Schema(description = "代理价1价格")
    private BigDecimal aponePrice;

    @Schema(description = "代理价1金额")
    private BigDecimal aponeAmount;

    @Schema(description = "代理价2价格")
    private BigDecimal aptwoPrice;

    @Schema(description = "代理价2金额")
    private BigDecimal aptwoAmount;

    @Schema(description = "商标代码")
    private String trademarkCode;

    @Schema(description = "所属运营中心")
    private String opcen;

    @Schema(description = "所属法人")
    private String legalEntity;

}