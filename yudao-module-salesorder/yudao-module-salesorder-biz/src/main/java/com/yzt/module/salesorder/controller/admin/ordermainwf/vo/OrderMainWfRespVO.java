package com.yzt.module.salesorder.controller.admin.ordermainwf.vo;

import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 合同评审流程 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderMainWfRespVO {

    @Schema(description = "自增长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20838")
    @ExcelProperty("自增长ID")
    private Integer id;

    @Schema(description = "评审单号")
    @ExcelProperty("评审单号")
    private String requestno;

    @Schema(description = "提交人")
    @ExcelProperty("提交人")
    private Integer submitter;

    @Schema(description = "部门")
    @ExcelProperty("部门")
    private Integer department;

    @Schema(description = "提交日期")
    @ExcelProperty("提交日期")
    private String submitdate;

    @Schema(description = "参考销售订单号")
    @ExcelProperty("参考销售订单号")
    private String reforder;

    @Schema(description = "客户名称", example = "赵六")
    @ExcelProperty("客户名称")
    private String customername;

    @Schema(description = "客户代码")
    @ExcelProperty("客户代码")
    private String customercode;

    @Schema(description = "客户订单号")
    @ExcelProperty("客户订单号")
    private String ctmorderno;

    @Schema(description = "报价单号")
    @ExcelProperty("报价单号")
    private String quoteno;

    @Schema(description = "项目编号")
    @ExcelProperty("项目编号")
    private String prjno;

    @Schema(description = "项目名称", example = "王五")
    @ExcelProperty("项目名称")
    private String prjname;

    @Schema(description = "客户下单确认日期")
    @ExcelProperty("客户下单确认日期")
    private String podate;

    @Schema(description = "请求交货期")
    @ExcelProperty("请求交货期")
    private String delydate;

    @Schema(description = "币种")
    @ExcelProperty("币种")
    private String currency;

    @Schema(description = "订单金额")
    @ExcelProperty("订单金额")
    private BigDecimal orderamt;

    @Schema(description = "付款方式")
    @ExcelProperty("付款方式")
    private String payway;

    @Schema(description = "技术条款附件")
    @ExcelProperty("技术条款附件")
    private List<FileRespDTO> techfiles;

    @Schema(description = "合同附件")
    @ExcelProperty("合同附件")
    private List<FileRespDTO> orderfiles;

    @Schema(description = "技术、质量要求")
    @ExcelProperty("技术、质量要求")
    private String techremark;

    @Schema(description = "检验、验收要求")
    @ExcelProperty("检验、验收要求")
    private String inspecremark;

    @Schema(description = "标志商标")
    @ExcelProperty("标志商标")
    private String markremark;

    @Schema(description = "油漆要求、包装要求")
    @ExcelProperty("油漆要求、包装要求")
    private String paintremark;

    @Schema(description = "文件要求")
    @ExcelProperty("文件要求")
    private String docremark;

    @Schema(description = "特殊要求")
    @ExcelProperty("特殊要求")
    private String specialremark;

    @Schema(description = "其他约定")
    @ExcelProperty("其他约定")
    private String otherremark;

    @Schema(description = "销售订单")
    @ExcelProperty("销售订单")
    private String salesorder;

    @Schema(description = "协调评审")
    @ExcelProperty("协调评审")
    private List<Long> teamwork;

    @Schema(description = "交货时间")
    @ExcelProperty("交货时间")
    private String delayremark;


    @Schema(description = "修改数据ID", example = "29248")
    @ExcelProperty("修改数据ID")
    private Integer mid;


    private String processInstanceId;


    @Schema(description = "返回标识")
    @ExcelProperty("返回标识")
    private String returnflg;

    @Schema(description = "返回信息")
    @ExcelProperty("返回信息")
    private String returnstr;

    @Schema(description = "MTR接口标识")
    @ExcelProperty("MTR接口标识")
    private String mtrflg;

    @Schema(description = "SRM接口标识")
    @ExcelProperty("SRM接口标识")
    private String srmflg;

    @Schema(description = "流程状态")
    @ExcelProperty("流程状态")
    private Integer status;

    @Schema(description = "修改项目")
    private String diffObj;

    @Schema(description = "是否需要线下评审")
    @ExcelProperty("是否需要线下评审")
    private Integer offlineReview;

    @Schema(description = "商标")
    @ExcelProperty("商标")
    private String brand;

    @Schema(description = "收款条件代码")
    @ExcelProperty("收款条件代码")
    private String recvCode;

    @Schema(description = "收款条件名称")
    @ExcelProperty("收款条件名称")
    private String recvName;

    @Schema(description = "汇率")
    @ExcelProperty("汇率")
    private BigDecimal exRate;

    @Schema(description = "销售订单性质")
    @ExcelProperty("销售订单性质")
    private String soNature;

    @Schema(description = "销售订单类型")
    @ExcelProperty("销售订单类型")
    private String soType;
}