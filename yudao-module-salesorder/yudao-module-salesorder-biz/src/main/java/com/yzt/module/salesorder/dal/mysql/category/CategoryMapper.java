package com.yzt.module.salesorder.dal.mysql.category;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.yzt.module.salesorder.dal.dataobject.category.CategoryDO;
import org.apache.ibatis.annotations.Mapper;
import com.yzt.module.salesorder.controller.admin.category.vo.*;

/**
 * 销售单别 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CategoryMapper extends BaseMapperX<CategoryDO> {

    default PageResult<CategoryDO> selectPage(CategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CategoryDO>()
                .eqIfPresent(CategoryDO::getCode, reqVO.getCode())
                .likeIfPresent(CategoryDO::getName, reqVO.getName())
                .orderByDesc(CategoryDO::getId));
    }

}