package com.yzt.module.salesorder.dal.dataobject.ordermain;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.handler.JsonbTypeHandlerMap;
import cn.iocoder.yudao.module.infra.api.file.dto.FileRespDTO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单主数据 DO
 *
 * <AUTHOR>
 */
@TableName(value="order_main",schema = "salesorder",autoResultMap = true)
@KeySequence("order_main_id_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderMainDO extends BaseDO {
    /**
     * 自增长ID
     */
    @TableId
    private Integer id;
    /**
     * 提交人
     */
    private Integer submitter;
    /**
     * 部门
     */
    private Integer department;
    /**
     * 提交日期
     */
    private String submitdate;
    /**
     * 参考销售订单号
     */
    private String reforder;
    /**
     * 客户名称
     */
    private String customername;
    /**
     * 客户代码
     */
    private String customercode;
    /**
     * 客户订单号
     */
    private String ctmorderno;
    /**
     * 报价单号
     */
    private String quoteno;
    /**
     * 项目编号
     */
    private String prjno;
    /**
     * 项目名称
     */
    private String prjname;
    /**
     * 客户下单确认日期
     */
    private String podate;
    /**
     * 请求交货期
     */
    private String delydate;
    /**
     * 币种
     */
    private String currency;
    /**
     * 订单金额
     */
    private BigDecimal orderamt;
    /**
     * 付款方式
     */
    private String payway;
    /**
     * 技术条款附件
     */
    @TableField(value = "techfiles", typeHandler = JsonbTypeHandlerMap.class)
    private List<FileRespDTO> techfiles;
    /**
     * 合同附件
     */
    @TableField(value = "orderfiles", typeHandler = JsonbTypeHandlerMap.class)
    private List<FileRespDTO> orderfiles;

    /**
     * 技术、质量要求
     */
    private String techremark;

    /**
     * 检验、验收要求
     */
    private String inspecremark;

    /**
     * 标志商标
     */
    private String markremark;

    /**
     * 油漆要求、包装要求
     */
    private String paintremark;

    /**
     * 文件要求
     */
    private String docremark;

    /**
     * 特殊要求
     */
    private String specialremark;

    /**
     * 其他约定
     */
    private String otherremark;

    /**
     * 销售订单
     */
    private String salesorder;

    /**
     * 协调评审
     */
    private String teamwork;

    /**
     * 交货时间
     */
    private String delayremark;

    /**
     * 是否需要线下评审
     */
    private Integer offlineReview;

    /**
     * 商标
     */
    private String brand;

    /**
     * 收款条件代码
     */
    private String recvCode;

    /**
     * 收款条件名称
     */
    private String recvName;

    /**
     * 汇率
     */
    private BigDecimal exRate;

    /**
     * 销售订单性质
     */
    private String soNature;

    /**
     * 销售订单类型
     */
    private String soType;

}