package com.yzt.module.salesorder.dal.mysql.ordersummarywf;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yzt.module.salesorder.controller.admin.ordersummarywf.vo.OrderSummaryWfPageReqVO;
import com.yzt.module.salesorder.dal.dataobject.ordersummarywf.OrderSummaryWfDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合同评审流程汇总数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderSummaryWfMapper extends BaseMapperX<OrderSummaryWfDO> {

    default PageResult<OrderSummaryWfDO> selectPage(OrderSummaryWfPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OrderSummaryWfDO>()
                .eqIfPresent(OrderSummaryWfDO::getMainid, reqVO.getMainid())
                .eqIfPresent(OrderSummaryWfDO::getItem, reqVO.getItem())
                .eqIfPresent(OrderSummaryWfDO::getCpmc, reqVO.getCpmc())
                .eqIfPresent(OrderSummaryWfDO::getGgxh, reqVO.getGgxh())
                .eqIfPresent(OrderSummaryWfDO::getZt, reqVO.getZt())
                .eqIfPresent(OrderSummaryWfDO::getFx, reqVO.getFx())
                .eqIfPresent(OrderSummaryWfDO::getFg, reqVO.getFg())
                .eqIfPresent(OrderSummaryWfDO::getFz, reqVO.getFz())
                .eqIfPresent(OrderSummaryWfDO::getMfm, reqVO.getMfm())
                .eqIfPresent(OrderSummaryWfDO::getSjbz, reqVO.getSjbz())
                .eqIfPresent(OrderSummaryWfDO::getLjbz, reqVO.getLjbz())
                .eqIfPresent(OrderSummaryWfDO::getJgcd, reqVO.getJgcd())
                .eqIfPresent(OrderSummaryWfDO::getWh, reqVO.getWh())
                .eqIfPresent(OrderSummaryWfDO::getSl, reqVO.getSl())
                .eqIfPresent(OrderSummaryWfDO::getDj, reqVO.getDj())
                .eqIfPresent(OrderSummaryWfDO::getXj, reqVO.getXj())
                .eqIfPresent(OrderSummaryWfDO::getBz, reqVO.getBz())
                .betweenIfPresent(OrderSummaryWfDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(OrderSummaryWfDO::getBwgNormNo, reqVO.getBwgNormNo())
                .eqIfPresent(OrderSummaryWfDO::getSpecSize, reqVO.getSpecSize())
                .eqIfPresent(OrderSummaryWfDO::getCustomerPml, reqVO.getCustomerPml())
                .eqIfPresent(OrderSummaryWfDO::getSalesUnit, reqVO.getSalesUnit())
                .eqIfPresent(OrderSummaryWfDO::getTaxPrice, reqVO.getTaxPrice())
                .eqIfPresent(OrderSummaryWfDO::getNotaxAmount, reqVO.getNotaxAmount())
                .eqIfPresent(OrderSummaryWfDO::getTaxAmount, reqVO.getTaxAmount())
                .eqIfPresent(OrderSummaryWfDO::getAponePrice, reqVO.getAponePrice())
                .eqIfPresent(OrderSummaryWfDO::getAponeAmount, reqVO.getAponeAmount())
                .eqIfPresent(OrderSummaryWfDO::getAptwoPrice, reqVO.getAptwoPrice())
                .eqIfPresent(OrderSummaryWfDO::getAptwoAmount, reqVO.getAptwoAmount())
                .eqIfPresent(OrderSummaryWfDO::getTrademarkCode, reqVO.getTrademarkCode())
                .eqIfPresent(OrderSummaryWfDO::getOpcen, reqVO.getOpcen())
                .eqIfPresent(OrderSummaryWfDO::getLegalEntity, reqVO.getLegalEntity())
                .betweenIfPresent(OrderSummaryWfDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(OrderSummaryWfDO::getId));
    }

    void deleteByMainId(String mainid);

    void insertOrderDetailWf(@Param("orderMainWfId")String orderMainWfId, @Param("mainid")String mainid);
}