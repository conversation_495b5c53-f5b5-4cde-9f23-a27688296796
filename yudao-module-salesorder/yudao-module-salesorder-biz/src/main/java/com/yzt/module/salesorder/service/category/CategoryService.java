package com.yzt.module.salesorder.service.category;

import java.util.*;
import javax.validation.*;
import com.yzt.module.salesorder.controller.admin.category.vo.*;
import com.yzt.module.salesorder.dal.dataobject.category.CategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 销售单别 Service 接口
 *
 * <AUTHOR>
 */
public interface CategoryService {

    /**
     * 创建销售单别
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid CategorySaveReqVO createReqVO);

    /**
     * 更新销售单别
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid CategorySaveReqVO updateReqVO);

    /**
     * 删除销售单别
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得销售单别
     *
     * @param id 编号
     * @return 销售单别
     */
    CategoryDO getCategory(Long id);

    /**
     * 获得销售单别分页
     *
     * @param pageReqVO 分页查询
     * @return 销售单别分页
     */
    PageResult<CategoryDO> getCategoryPage(CategoryPageReqVO pageReqVO);

}