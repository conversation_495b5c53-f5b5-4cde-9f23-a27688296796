# 测试环境：只在打包时使用
NODE_ENV=production

VITE_DEV=false

# 请求路径
VITE_BASE_URL='http://180.106.149.4:8060'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='http://180.106.149.4:8060/admin-api/infra/file/upload-id'

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist-test

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://mall.yudao.iocoder.cn'

# GoView域名
VITE_GOVIEW_URL='http://127.0.0.1:3000'