package com.valve.module.base.service.exchangerate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.valve.module.base.controller.admin.exchangerate.vo.ExchangeRatePageReqVO;
import com.valve.module.base.controller.admin.exchangerate.vo.ExchangeRateSaveReqVO;
import com.valve.module.base.dal.dataobject.exchangerate.ExchangeRateDO;
import com.valve.module.base.dal.dataobject.tcazjv.TcAzjVDo;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

/**
 * 汇率 Service 接口
 *
 * <AUTHOR>
 */
public interface ExchangeRateService {

    /**
     * 创建汇率
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createExchangeRate(@Valid ExchangeRateSaveReqVO createReqVO);

    /**
     * 更新汇率
     *
     * @param updateReqVO 更新信息
     */
    void updateExchangeRate(@Valid ExchangeRateSaveReqVO updateReqVO);

    /**
     * 删除汇率
     *
     * @param id 编号
     */
    void deleteExchangeRate(Integer id);

    /**
     * 获得汇率
     *
     * @param id 编号
     * @return 汇率
     */
    ExchangeRateDO getExchangeRate(Integer id);

    /**
     * 获得汇率分页
     *
     * @param pageReqVO 分页查询
     * @return 汇率分页
     */
    PageResult<ExchangeRateDO> getExchangeRatePage(ExchangeRatePageReqVO pageReqVO);

    void deleteExchangeRateByMonth(LocalDate monthStar, LocalDate monthEnd);

    void insertBatch(List<TcAzjVDo> tcAzjVDoList, LocalDate monthStart, LocalDate monthEnd);
}