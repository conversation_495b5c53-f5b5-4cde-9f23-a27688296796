package com.valve.module.base.job;


import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.valve.module.base.dal.dataobject.tcazjv.TcAzjVDo;
import com.valve.module.base.dal.mysql.tcazjv.TcAzjVMapper;
import com.valve.module.base.service.exchangerate.ExchangeRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * @Description: 定时从ERP汇率视图迁移汇率信息定时任务
 */
@Component
@Slf4j
public class ErpExchangeRateSyncJob implements JobHandler {

    @Resource
    private TcAzjVMapper tcAzjVMapper;

    @Resource
    private ExchangeRateService exchangeRateService;

    @Override
    public String execute(String param) throws Exception {
        // 1.记录定时任务开始执行时间
        long startTime = System.currentTimeMillis();

        try {
            // 2.获取当前日期
            LocalDate today = LocalDate.now();
            // 获取当前年份
            int currentYear = today.getYear();
            // 获取当前月份
            int currentMonth = today.getMonthValue();
            // 计算当月第一天
            LocalDate firstDayOfMonth = today.with(TemporalAdjusters.firstDayOfMonth());
            // 计算当月最后一天
            LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
            // 将年份和月份拼接成"yyyyMM"格式的字符串
            String yearMonthStr = String.format("%d%02d", currentYear, currentMonth);

            log.info("[ERP汇率数据同步任务] 开始执行，当前处理月份: {}", yearMonthStr);

            //3.获取ERP汇率数据
            List<TcAzjVDo> tcAzjVDoList = tcAzjVMapper.selectErpExchangeRateByMonth(yearMonthStr);
            if (CollectionUtils.isEmpty(tcAzjVDoList)) {
                log.info("[ERP汇率数据同步任务] ERP没有需要同步的汇率数据");
                return "ERP没有需要同步的汇率数据";
            }

            log.info("[ERP汇率数据同步任务] 查询到 {} 条ERP汇率数据", tcAzjVDoList.size());

            //4.根据币种和月份删除旧数据
            LocalDate monthStart = firstDayOfMonth;
            LocalDate monthEnd = lastDayOfMonth;

            log.info("[ERP汇率数据同步任务] 开始删除 {} 至 {} 期间的旧汇率数据", monthStart, monthEnd);
            exchangeRateService.deleteExchangeRateByMonth(monthStart, monthEnd);

            //5.批量插入新数据
            log.info("[ERP汇率数据同步任务] 开始批量插入 {} 条汇率数据", tcAzjVDoList.size());
            exchangeRateService.insertBatch(tcAzjVDoList, monthStart, monthEnd);

            //6.记录定时任务结束执行时间
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            String result = String.format("成功同步 %d 条汇率信息，耗时: %d ms", tcAzjVDoList.size(), duration);
            log.info("[ERP汇率数据同步任务] {}", result);

            //7.返回结果
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            log.error("[ERP汇率数据同步任务] 执行过程中发生异常，耗时: {} ms", duration, e);
            throw e;
        }
    }
}
