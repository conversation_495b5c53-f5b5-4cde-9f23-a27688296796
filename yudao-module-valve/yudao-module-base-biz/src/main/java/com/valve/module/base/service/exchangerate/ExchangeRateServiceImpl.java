package com.valve.module.base.service.exchangerate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.valve.module.base.controller.admin.exchangerate.vo.ExchangeRatePageReqVO;
import com.valve.module.base.controller.admin.exchangerate.vo.ExchangeRateSaveReqVO;
import com.valve.module.base.dal.dataobject.exchangerate.ExchangeRateDO;
import com.valve.module.base.dal.dataobject.tcazjv.TcAzjVDo;
import com.valve.module.base.dal.mysql.exchangerate.ExchangeRateMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.valve.module.base.enums.ErrorCodeConstants.EXCHANGE_RATE_NOT_EXISTS;

/**
 * 汇率 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ExchangeRateServiceImpl implements ExchangeRateService {

    @Resource
    private ExchangeRateMapper exchangeRateMapper;

    @Override
    public Integer createExchangeRate(ExchangeRateSaveReqVO createReqVO) {
        // 插入
        ExchangeRateDO exchangeRate = BeanUtils.toBean(createReqVO, ExchangeRateDO.class);
        exchangeRateMapper.insert(exchangeRate);
        // 返回
        return exchangeRate.getId();
    }

    @Override
    public void updateExchangeRate(ExchangeRateSaveReqVO updateReqVO) {
        // 校验存在
        validateExchangeRateExists(updateReqVO.getId());
        // 更新
        ExchangeRateDO updateObj = BeanUtils.toBean(updateReqVO, ExchangeRateDO.class);
        exchangeRateMapper.updateById(updateObj);
    }

    @Override
    public void deleteExchangeRate(Integer id) {
        // 校验存在
        validateExchangeRateExists(id);
        // 删除
        exchangeRateMapper.deleteById(id);
    }

    private void validateExchangeRateExists(Integer id) {
        if (exchangeRateMapper.selectById(id) == null) {
            throw exception(EXCHANGE_RATE_NOT_EXISTS);
        }
    }

    @Override
    public ExchangeRateDO getExchangeRate(Integer id) {
        return exchangeRateMapper.selectById(id);
    }

    @Override
    public PageResult<ExchangeRateDO> getExchangeRatePage(ExchangeRatePageReqVO pageReqVO) {
        return exchangeRateMapper.selectPage(pageReqVO);
    }

    @Override
    public void deleteExchangeRateByMonth(LocalDate monthStart, LocalDate monthEnd) {
        exchangeRateMapper.deleteExchangeRateByMonth(monthStart, monthEnd);
    }

    @Override
    public void insertBatch(List<TcAzjVDo> tcAzjVDoList, LocalDate monthStart, LocalDate monthEnd) {
        List<ExchangeRateDO> rateDOList = tcAzjVDoList.stream().map(tcAzjVDo -> {
            ExchangeRateDO exchangeRateDO = new ExchangeRateDO();
            exchangeRateDO.setFromCurrency(tcAzjVDo.getFromCurrency());
            exchangeRateDO.setToCurrency("RMB");
            exchangeRateDO.setExchangeRate(tcAzjVDo.getExchangeRate());
            exchangeRateDO.setValidFrom(monthStart);
            exchangeRateDO.setValidTo(monthEnd);
            return exchangeRateDO;
        }).collect(Collectors.toList());
        exchangeRateMapper.insertBatch(rateDOList);
    }
}