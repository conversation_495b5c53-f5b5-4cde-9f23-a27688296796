package com.valve.module.base.dal.dataobject.exchangerate;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 汇率 DO
 *
 * <AUTHOR>
 */
@TableName(value = "exchange_rate", autoResultMap = true)
@KeySequence("exchange_rate_id_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRateDO extends BaseDO {

    /**
     * 自增长ID
     */
    @TableId
    private Integer id;
    /**
     * 从币种
     */
    private String fromCurrency;
    /**
     * 到币种
     */
    private String toCurrency;
    /**
     * 汇率
     */
    private BigDecimal exchangeRate;
    /**
     * 有效期起
     */
    private LocalDate validFrom;
    /**
     * 有效期止
     */
    private LocalDate validTo;

}