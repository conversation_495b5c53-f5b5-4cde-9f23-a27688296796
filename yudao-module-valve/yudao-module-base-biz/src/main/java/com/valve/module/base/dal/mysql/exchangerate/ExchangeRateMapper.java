package com.valve.module.base.dal.mysql.exchangerate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.valve.module.base.controller.admin.exchangerate.vo.ExchangeRatePageReqVO;
import com.valve.module.base.dal.dataobject.exchangerate.ExchangeRateDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * 汇率 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ExchangeRateMapper extends BaseMapperX<ExchangeRateDO> {

    default PageResult<ExchangeRateDO> selectPage(ExchangeRatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ExchangeRateDO>()
                .eqIfPresent(ExchangeRateDO::getFromCurrency, reqVO.getFromCurrency())
                .eqIfPresent(ExchangeRateDO::getToCurrency, reqVO.getToCurrency())
                .eqIfPresent(ExchangeRateDO::getExchangeRate, reqVO.getExchangeRate())
                .eqIfPresent(ExchangeRateDO::getValidFrom, reqVO.getValidFrom())
                .eqIfPresent(ExchangeRateDO::getValidTo, reqVO.getValidTo())
                .orderByDesc(ExchangeRateDO::getId));
    }

    @Delete("DELETE FROM exchange_rate WHERE valid_from >= #{monthStart} AND valid_to <= #{monthEnd}")
    void deleteExchangeRateByMonth(@Param("monthStart") LocalDate monthStart, @Param("monthEnd") LocalDate monthEnd);
}