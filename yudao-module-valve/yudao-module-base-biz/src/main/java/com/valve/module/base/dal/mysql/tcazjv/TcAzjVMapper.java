package com.valve.module.base.dal.mysql.tcazjv;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.valve.module.base.dal.dataobject.tcazjv.TcAzjVDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: ERP汇率视图mapper层
 */
@Slave
@Mapper
public interface TcAzjVMapper {
    @Select("SELECT " +
            "币种 as fromCurrency, 汇率 as exchangeRate, 月份 as mon " +
            "FROM TC_AZJ_V " +
            "WHERE 月份 = #{yearMonthStr}")
    List<TcAzjVDo> selectErpExchangeRateByMonth(String yearMonthStr);
}
