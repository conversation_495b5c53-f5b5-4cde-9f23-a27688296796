package com.yzt.webservice.salesorder;

import com.yzt.webservice.purchaseorderchange.PruchaseOrderChangeApplication;
import com.yzt.webservice.salesorder.service.SalesOrderBusinessService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Description: 测试销售订单WebService请求
 */
@SpringBootTest(classes = SalesOrderApplication.class)
public class SalesOrderTest {

    @Resource
    private SalesOrderBusinessService salesOrderBusinessService;

    @Test
    public void testSubmitSalesOrder() {
        salesOrderBusinessService.submitSalesOrder();
    }
}
