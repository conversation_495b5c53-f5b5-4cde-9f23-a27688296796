package com.yzt.webservice.salesorder.model;

import com.yzt.webservice.salesorder.adapter.RequestAdapter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "request", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay")
@XmlJavaTypeAdapter(RequestAdapter.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Request {

   @XmlElement(name = "Request")
   private RequestWrapper requestWrapper;
}