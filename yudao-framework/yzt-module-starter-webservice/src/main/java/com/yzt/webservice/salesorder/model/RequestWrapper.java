package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Request")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RequestWrapper {

    @XmlElement(name = "Access")
    private Access access;

    @XmlElement(name = "RequestContent")
    private RequestContent requestContent;
}