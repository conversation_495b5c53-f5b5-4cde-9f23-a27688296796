package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.*;

@XmlRootElement(name = "SalesOrderData.createResponse", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CreateResponse", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay", propOrder = {
        "response"
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateResponse {

    @XmlElement(name = "response", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay", required = true)
    private String response;

}