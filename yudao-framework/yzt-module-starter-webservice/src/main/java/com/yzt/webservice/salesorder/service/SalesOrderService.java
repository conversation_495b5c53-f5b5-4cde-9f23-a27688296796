package com.yzt.webservice.salesorder.service;

import com.sun.xml.bind.marshaller.NamespacePrefixMapper;
import com.yzt.webservice.salesorder.dto.SalesOrderDTO;
import com.yzt.webservice.salesorder.dto.SalesOrderDetailDTO;
import com.yzt.webservice.salesorder.dto.SalesOrderResponseDTO;
import com.yzt.webservice.salesorder.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.ws.WebServiceMessage;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.soap.SoapMessage;

import javax.annotation.Resource;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.transform.stream.StreamResult;
import java.io.IOException;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 销售订单发送请求
 */
@Service
public class SalesOrderService {

    private static final Logger logger = LoggerFactory.getLogger(SalesOrderService.class);

    @Resource
    private WebServiceTemplate webServiceTemplate;

    /**
     * 构造并发送销售订单请求
     * @param salesOrderDTO 销售订单主表数据
     * @param detailList 销售订单明细数据列表
     * @return PurchaseOrderResponseDTO 响应结果
     */
    public SalesOrderResponseDTO sendSalesOrderRequest(SalesOrderDTO salesOrderDTO, List<SalesOrderDetailDTO> detailList) {
        CreateRequest createRequest = new CreateRequest();
        Request request = buildRequest(salesOrderDTO, detailList);
        createRequest.setRequest(request);

        printRequestXml(createRequest); // 可选：打印请求内容

        try {
            // 使用 marshalSendAndReceive 发送请求并接收响应
            Object response = webServiceTemplate.marshalSendAndReceive(
                    "http://192.168.0.131:6384/ws/r/aws_ttsrv2?WSDL",
                    createRequest,
                    new WebServiceMessageCallback() {
                        @Override
                        public void doWithMessage(WebServiceMessage message) throws IOException {
                            if (message instanceof SoapMessage) {
                                SoapMessage soapMessage = (SoapMessage) message;
                                // 设置 SOAP Action
                                soapMessage.setSoapAction("http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay/SalesOrderData.createRequest");
                            }
                        }
                    }
            );

            // 处理响应并返回SalesOrderResponseDTO结果
            return processResponseForStatusCode(response);
        } catch (Exception e) {
            logger.error("发送销售订单请求失败: " + e.getMessage(), e);
            // 发生异常时返回错误信息
            return new SalesOrderResponseDTO(1, "发送销售订单请求失败: " + e.getMessage(), null);
        }
    }

    /**
     * 处理响应对象并提取状态码和描述
     * @param response 原始响应对象
     * @return SalesOrderResponseDTO 响应结果
     */
    private SalesOrderResponseDTO processResponseForStatusCode(Object response) {
        try {
            String responseXml;

            if (response instanceof String) {
                responseXml = (String) response;
            } else if (response instanceof CreateResponse) {
                // 假设 CreateResponse 有一个 getResponse() 方法返回 String
                responseXml = ((CreateResponse) response).getResponse();
            } else {
                logger.warn("未知的响应类型: {}", response.getClass().getName());
                return new SalesOrderResponseDTO(1, "未知的响应类型: " + response.getClass().getName(), null);
            }

            logger.info("响应 XML 内容：\n{}", responseXml);

            // 解析XML获取状态码和描述
            return parseStatusFromXml(responseXml);
        } catch (Exception e) {
            logger.warn("处理响应时发生错误", e);
            return new SalesOrderResponseDTO(1, "处理响应时发生错误: " + e.getMessage(), null);
        }
    }

    // 解析XML获取状态码和描述
// 解析XML获取状态码、描述和ERP销售订单编号
    private SalesOrderResponseDTO parseStatusFromXml(String xml) {
        try {
            // 尝试解析完整的 XML 结构
            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(xml.getBytes("UTF-8")));

            // 查找 Status 节点
            org.w3c.dom.NodeList statusNodes = doc.getElementsByTagName("Status");
            String code = null;
            String description = null;

            if (statusNodes.getLength() > 0) {
                org.w3c.dom.Element statusElement = (org.w3c.dom.Element) statusNodes.item(0);
                code = statusElement.getAttribute("code");
                description = statusElement.getAttribute("description");
            }

            // 查找 oea01 字段节点 为销售订单号
            String erpSalesOrderCode = null;
            org.w3c.dom.NodeList fieldNodes = doc.getElementsByTagName("Field");
            for (int i = 0; i < fieldNodes.getLength(); i++) {
                org.w3c.dom.Element fieldElement = (org.w3c.dom.Element) fieldNodes.item(i);
                if ("oea01".equals(fieldElement.getAttribute("name"))) {
                    erpSalesOrderCode = fieldElement.getAttribute("value");
                    break;
                }
            }

            // 处理状态码
            if (code != null && !code.isEmpty()) {
                // 将字符串形式的状态码转换为整数
                int statusCode = -1;
                try {
                    statusCode = Integer.parseInt(code);
                } catch (NumberFormatException e) {
                    logger.warn("无法解析状态码: {}", code);
                }
                return new SalesOrderResponseDTO(statusCode, description != null ? description : "", erpSalesOrderCode != null ? erpSalesOrderCode : "");
            }

            // 如果找不到Status节点，返回错误信息
            return new SalesOrderResponseDTO(1, "响应XML中未找到Status节点", "");
        } catch (Exception e) {
            logger.warn("使用DOM解析XML时出错", e);
            return new SalesOrderResponseDTO(1, "解析响应XML时出错: " + e.getMessage(), "");
        }
    }





    /**
     * 打印请求的 XML 内容
     */
    private void printRequestXml(CreateRequest createRequest) {
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(CreateRequest.class, CreateResponse.class);
            Marshaller marshaller = jaxbContext.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            marshaller.setProperty("com.sun.xml.bind.namespacePrefixMapper", new CustomNamespacePrefixMapper());

            StringWriter requestWriter = new StringWriter();
            marshaller.marshal(createRequest, new StreamResult(requestWriter));
            String rawXml = requestWriter.toString();

            logger.info("发送的 XML 请求内容：\n{}", rawXml);
        } catch (JAXBException e) {
            logger.error("序列化请求为 XML 时出错", e);
        }
    }

    /**
     * 构造 RequestContent（含业务数据）
     */
    private Request buildRequest(SalesOrderDTO salesOrderDTO, List<SalesOrderDetailDTO> detailList) {
        Request request = new Request();
        RequestWrapper requestWrapper = new RequestWrapper();
        request.setRequestWrapper(requestWrapper);

        // 构造 Access（略，保持不变）
        Access access = new Access();
        String username = "tiptop";
        String password = "";
        access.setAuthentication(new Authentication(username, password));
        access.setConnection(new Connection("CRM", "***********"));
        access.setOrganization(new Organization("CHSV"));
        access.setLocale(new Locale("zh_cn"));
        requestWrapper.setAccess(access);

        // 构造 Parameter（略，保持不变）
        Parameter parameter = new Parameter();
        Record parameterRecord = new Record();
        List<Field> parameterFields = new ArrayList<>();
        parameterFields.add(new Field("ws_name", "SalesOrderData.create"));
        parameterFields.add(new Field("status", "Y"));
        parameterRecord.setFields(parameterFields);
        parameter.setRecord(parameterRecord);
        requestWrapper.setRequestContent(new RequestContent());
        requestWrapper.getRequestContent().setParameter(parameter);

        // 构造 Document
        Document document = new Document();
        RecordSet recordSet = new RecordSet();
        recordSet.setId("1");

        // 构造 Master（主表）
        Master master = new Master();
        master.setName("oea_file");
        Record masterRecord = new Record();
        List<Field> masterFields = new ArrayList<>();

        // 使用 SalesOrderDTO 的字段赋值
        masterFields.add(new Field("oea00", salesOrderDTO.getOea00()));
        masterFields.add(new Field("oea08", salesOrderDTO.getOea08()));
        masterFields.add(new Field("oea01", salesOrderDTO.getOea01()));
        masterFields.add(new Field("oea46", salesOrderDTO.getOea46()));
        masterFields.add(new Field("oea02", new SimpleDateFormat("yyyy-MM-dd").format(salesOrderDTO.getOea02()))); // 日期格式化
        masterFields.add(new Field("oea03", salesOrderDTO.getOea03()));
        masterFields.add(new Field("oea04", salesOrderDTO.getOea04()));
        masterFields.add(new Field("oea17", salesOrderDTO.getOea17()));
        masterFields.add(new Field("ta_oea01", salesOrderDTO.getTa_oea01()));
        masterFields.add(new Field("ta_oea02", salesOrderDTO.getTa_oea02()));
        masterFields.add(new Field("ta_oea03", salesOrderDTO.getTa_oea03()));
        masterFields.add(new Field("oea14", salesOrderDTO.getOea14()));
        masterFields.add(new Field("oea15", salesOrderDTO.getOea15()));
        masterFields.add(new Field("oeaud13", new SimpleDateFormat("yyyy-MM-dd").format(salesOrderDTO.getOeaud13()))); // 日期格式化
        masterFields.add(new Field("ta_oea07", salesOrderDTO.getTa_oea07()));
        masterFields.add(new Field("ta_oea08", salesOrderDTO.getTa_oea08()));
        masterFields.add(new Field("ta_oea09", salesOrderDTO.getTa_oea09()));
        masterFields.add(new Field("ta_oea10", salesOrderDTO.getTa_oea10()));
        masterFields.add(new Field("ta_oea11", salesOrderDTO.getTa_oea11()));
        masterFields.add(new Field("ta_oea12", salesOrderDTO.getTa_oea12()));
        masterFields.add(new Field("ta_oea13", salesOrderDTO.getTa_oea13()));
        masterFields.add(new Field("oea23", salesOrderDTO.getOea23()));
        masterFields.add(new Field("oea21", salesOrderDTO.getOea21()));
        masterFields.add(new Field("oea32", salesOrderDTO.getOea32()));
        masterFields.add(new Field("ta_oea04", salesOrderDTO.getTa_oea04()));
        masterFields.add(new Field("oea162", salesOrderDTO.getOea162()));
        masterFields.add(new Field("oea163", salesOrderDTO.getOea163()));
        masterFields.add(new Field("oea261", salesOrderDTO.getOea261() != null ? salesOrderDTO.getOea261().toString() : ""));
        masterFields.add(new Field("oea262", salesOrderDTO.getOea262() != null ? salesOrderDTO.getOea262().toString() : ""));
        masterFields.add(new Field("oea263", salesOrderDTO.getOea263() != null ? salesOrderDTO.getOea263().toString() : ""));
        masterFields.add(new Field("oea25", salesOrderDTO.getOea25() != null ? salesOrderDTO.getOea25().toString() : ""));
        masterFields.add(new Field("ta_oea15", salesOrderDTO.getTa_oea15() != null ? salesOrderDTO.getTa_oea15().toString() : "N"));
        masterFields.add(new Field("oea901", salesOrderDTO.getOea901()));
        masterFields.add(new Field("oea68", salesOrderDTO.getOea68()));
        masterFields.add(new Field("oeaud05", salesOrderDTO.getOeaud05()));

        masterRecord.setFields(masterFields);
        master.setRecord(masterRecord);
        recordSet.setMaster(master);


        masterRecord.setFields(masterFields);
        master.setRecord(masterRecord);
        recordSet.setMaster(master);

        // 构造 Detail（明细）
        Detail detail = new Detail();
        detail.setName("oeb_file");
        List<Record> detailRecords = new ArrayList<>();

        for (SalesOrderDetailDTO detailItem : detailList) {
            Record detailRecord = new Record();
            List<Field> detailFields = new ArrayList<>();

            // 根据SalesOrderDetailDTO设置字段值
            detailFields.add(new Field("oeb03", detailItem.getOeb03() != null ? detailItem.getOeb03().toString() : ""));
            detailFields.add(new Field("ta_oeb004", detailItem.getTa_oeb004() != null ? detailItem.getTa_oeb004() : ""));
            detailFields.add(new Field("oeb04", detailItem.getOeb04() != null ? detailItem.getOeb04() : ""));
            detailFields.add(new Field("oeb06", detailItem.getOeb06() != null ? detailItem.getOeb06() : ""));
            detailFields.add(new Field("ta_oeb008", detailItem.getTa_oeb008() != null ? detailItem.getTa_oeb008() : ""));
            detailFields.add(new Field("ta_oeb009", detailItem.getTa_oeb009() != null ? detailItem.getTa_oeb009() : ""));
            detailFields.add(new Field("oeb11", detailItem.getOeb11() != null ? detailItem.getOeb11() : ""));
            detailFields.add(new Field("oeb15", detailItem.getOeb15() != null ? new SimpleDateFormat("yyyy-MM-dd").format(detailItem.getOeb15()) : ""));
            detailFields.add(new Field("oeb12", detailItem.getOeb12() != null ? detailItem.getOeb12().toString() : ""));
            detailFields.add(new Field("oeb05", detailItem.getOeb05() != null ? detailItem.getOeb05().toString() : ""));
            detailFields.add(new Field("oeb13", detailItem.getOeb13() != null ? detailItem.getOeb13().toString() : ""));
            detailFields.add(new Field("oeb14", detailItem.getOeb14() != null ? detailItem.getOeb14().toString() : ""));
            detailFields.add(new Field("oeb14t", detailItem.getOeb14t() != null ? detailItem.getOeb14t().toString() : ""));
            detailFields.add(new Field("ta_oeb001", detailItem.getTa_oeb001() != null ? detailItem.getTa_oeb001().toString() : ""));
            detailFields.add(new Field("ta_oeb006", detailItem.getTa_oeb006() != null ? detailItem.getTa_oeb006().toString() : ""));
            detailFields.add(new Field("ta_oeb002", detailItem.getTa_oeb002() != null ? detailItem.getTa_oeb002().toString() : ""));
            detailFields.add(new Field("ta_oeb007", detailItem.getTa_oeb007() != null ? detailItem.getTa_oeb007().toString() : ""));
            detailFields.add(new Field("oebud01", detailItem.getOebud01() != null ? detailItem.getOebud01() : ""));
            detailFields.add(new Field("ta_oeb003", detailItem.getTa_oeb003() != null ? detailItem.getTa_oeb003() : ""));
            detailFields.add(new Field("ta_oeb005", detailItem.getTa_oeb005() != null ? detailItem.getTa_oeb005() : ""));

            detailRecord.setFields(detailFields);
            detailRecords.add(detailRecord);
        }


        detail.setRecords(detailRecords);
        recordSet.setDetail(detail);
        document.setRecordSet(recordSet);
        requestWrapper.getRequestContent().setDocument(document);

        return request;
    }

    /**
     * 自定义命名空间前缀映射器
     */
    private static class CustomNamespacePrefixMapper extends NamespacePrefixMapper {
        @Override
        public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
            if ("http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay".equals(namespaceUri)) {
                return "tip";
            } else if ("http://schemas.xmlsoap.org/soap/envelope/".equals(namespaceUri)) {
                return "soapenv";
            }
            return suggestion;
        }
    }

}
