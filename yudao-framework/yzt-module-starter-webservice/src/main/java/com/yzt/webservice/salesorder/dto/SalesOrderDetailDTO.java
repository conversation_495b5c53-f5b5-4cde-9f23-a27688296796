package com.yzt.webservice.salesorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 销售订单明细表WebService请求参数DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderDetailDTO {

    /**
     * 销售订单项次号
     */
    private BigDecimal oeb03;

    /**
     * 销售订单ITEM号
     */
    private String ta_oeb004;

    /**
     * 物料号
     */
    private String oeb04;

    /**
     * 物料描述
     */
    private String oeb06;

    /**
     * 图号标准号
     */
    private String ta_oeb008;

    /**
     * 规格尺寸
     */
    private String ta_oeb009;

    /**
     * 客户产品型号
     */
    private String oeb11;

    /**
     * 约定交货日期
     */
    private Date oeb15;

    /**
     * 销售数量
     */
    private BigDecimal oeb12;

    /**
     * 销售单位
     */
    private BigDecimal oeb05;

    /**
     * 含税单价
     */
    private BigDecimal oeb13;

    /**
     * 税前金额
     */
    private BigDecimal oeb14;

    /**
     * 含税金额
     */
    private BigDecimal oeb14t;

    /**
     * 代理价1单价
     */
    private BigDecimal ta_oeb001;

    /**
     * 代理价1金额
     */
    private BigDecimal ta_oeb006;

    /**
     * 代理价2单价
     */
    private BigDecimal ta_oeb002;

    /**
     * 代理价2金额
     */
    private BigDecimal ta_oeb007;

    /**
     * 备注
     */
    private String oebud01;

    /**
     * 商标代号
     */
    private String ta_oeb003;

    /**
     * 所属营运中心
     */
    private String oebplant;

    /**
     * 所属法人
     */
    private String oeblegal;

    /**
     * 位号
     */
    private String ta_oeb005;

}
