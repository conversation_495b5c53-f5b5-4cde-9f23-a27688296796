package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Envelope")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Envelope {

    @XmlElement(name = "Header")
    private Header header;

    @XmlElement(name = "Body")
    private Body body;
}