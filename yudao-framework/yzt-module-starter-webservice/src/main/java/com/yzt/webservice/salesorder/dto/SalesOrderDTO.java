package com.yzt.webservice.salesorder.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 销售订单主表WebService请求参数DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesOrderDTO {

    /**
     * 销售订单类型
     */
    private String oea00;

    /**
     * 销售订单性质
     */
    private String oea08;


    /**
     * 项目编号单别
     */
    private String oea01;

    /**
     * 项目编号单别
     */
    private String oea46;

    /**
     * 订单日期
     */
    private Date oea02;

    /**
     * 账款客户编号
     */
    private String oea03;

    /**
     * 送货客户编号
     */
    private String oea04;

    /**
     * 收款客户编号
     */
    private String oea17;

    /**
     * 客户订单号
     */
    private String ta_oea01;

    /**
     * 客户项目名称
     */
    private String ta_oea02;

    /**
     * 商标代号
     */
    private String ta_oea03;

    /**
     * 业务员工号
     */
    private String oea14;

    /**
     * 部门编号
     */
    private String oea15;

    /**
     * 交货期
     */
    private Date oeaud13;

    /**
     * 技术、质量要求
     */
    private String ta_oea07;

    /**
     * 检验、验收要求
     */
    private String ta_oea08;

    /**
     * 商标要求
     */
    private String ta_oea09;

    /**
     * 油漆、包装要求
     */
    private String ta_oea10;

    /**
     * 文件要求
     */
    private String ta_oea11;

    /**
     * 特殊要求
     */
    private String ta_oea12;

    /**
     * 其他约定
     */
    private String ta_oea13;

    /**
     * 币种
     */
    private String oea23;

    /**
     * 税种
     */
    private String oea21;

    /**
     * 汇率
     */
    private BigDecimal oea24;

    /**
     * 允许超交率
     */
    private BigDecimal oea09;

    /**
     * 订单总税前金额
     */
    private BigDecimal oea61;

    /**
     * 已出货税前金额
     */
    private BigDecimal oea62;

    /**
     * 被结案税前金额
     */
    private BigDecimal oea63;

    /**
     * 结算方式
     */
    private BigDecimal oea85;

    /**
     * 收款条件编号
     */
    private String oea32;

    /**
     * 销售内勤工号
     */
    private String ta_oea04;

    /**
     * 所属营运中心
     */
    private String oeaplant;

    /**
     * 所属法人
     */
    private String oealegal;

    /**
     * 审核时间
     */
    private String oeacont;

    /**
     * 审核人员
     */
    private String oeaconu;

    /**
     * 资料建立者
     */
    private String oeaoriu;

    /**
     * 资料建立部门
     */
    private String oeaorig;

    /**
     * 出货应收比例
     */
    private String oea162;

    /**
     * 尾款应收比例
     */
    private String oea163;

    /**
     * 订金金额
     */
    private BigDecimal oea261;

    /**
     * 出货金额
     */
    private BigDecimal oea262;

    /**
     * 尾款金额
     */
    private BigDecimal oea263;

    /**
     * 销售分类一
     */
    private BigDecimal oea25;

    /**
     * 客户是否特殊要求 默认'N'
     */
    private BigDecimal ta_oea15;

    /**
     * 是否多角贸易订单Y/N
     */
    private String oea901;

    /**
     * 外部接入数据分类
     */
    private String oea68;

    /**
     * 外部接入数据ID
     */
    private String oeaud05;

}
