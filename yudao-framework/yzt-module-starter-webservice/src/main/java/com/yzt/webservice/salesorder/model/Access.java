package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Access")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Access {
    // 修改为使用@XmlAttribute以匹配XML结构
    @XmlElement(name = "Authentication")
    private Authentication authentication;

    @XmlElement(name = "Connection")
    private Connection connection;

    @XmlElement(name = "Organization")
    private Organization organization;

    @XmlElement(name = "Locale")
    private Locale locale;
}