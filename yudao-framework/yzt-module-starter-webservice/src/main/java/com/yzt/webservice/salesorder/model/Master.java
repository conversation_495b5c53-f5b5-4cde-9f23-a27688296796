package com.yzt.webservice.salesorder.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Master")
public class Master {

    @XmlAttribute(name = "name")
    private String name;
    @XmlElement(name = "Record")
    private Record record;

}
