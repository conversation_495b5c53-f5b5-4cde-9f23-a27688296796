package com.yzt.webservice.salesorder.service;

import com.yzt.webservice.salesorder.dto.SalesOrderDTO;
import com.yzt.webservice.salesorder.dto.SalesOrderDetailDTO;
import com.yzt.webservice.salesorder.dto.SalesOrderResponseDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Service
public class SalesOrderBusinessService {

    @Resource
    private  SalesOrderService salesOrderService;


    public void submitSalesOrder() {
        // 1. 准备主表数据
        SalesOrderDTO salesOrderDTO = new SalesOrderDTO();
        salesOrderDTO.setOea00("1");
        salesOrderDTO.setOea08("1");
        salesOrderDTO.setOea01("20QC");
        salesOrderDTO.setOea46("20QC");
        try {
            salesOrderDTO.setOea02(new SimpleDateFormat("yyyy-MM-dd").parse("2022-12-15"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        salesOrderDTO.setOea03("92313");
        salesOrderDTO.setOea04("92313");
        salesOrderDTO.setOea17("92313");
        salesOrderDTO.setOea14("1851");
        salesOrderDTO.setOea15("1070");
        try {
            salesOrderDTO.setOeaud13(new SimpleDateFormat("yyyy-MM-dd").parse("2022-12-31"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        salesOrderDTO.setTa_oea01("客户订单号");
        salesOrderDTO.setTa_oea02("客户项目名称");
        salesOrderDTO.setTa_oea03("001");
        salesOrderDTO.setTa_oea07("技术、质量要求");
        salesOrderDTO.setTa_oea08("检验、验收要求");
        salesOrderDTO.setTa_oea09("商标要求");
        salesOrderDTO.setTa_oea10("油漆、包装要求");
        salesOrderDTO.setTa_oea11("文件要求");
        salesOrderDTO.setTa_oea12("特殊要求");
        salesOrderDTO.setTa_oea13("其他约定");
        salesOrderDTO.setTa_oea04("1851");
        salesOrderDTO.setOea901("N");
        salesOrderDTO.setOea68("YSN");
        salesOrderDTO.setOeaud05("外部接入数据ID不可重复");

        // 2. 准备明细数据
        List<SalesOrderDetailDTO> details = new ArrayList<>();

        // 第一行明细
        SalesOrderDetailDTO detail1 = new SalesOrderDetailDTO();
        detail1.setOeb03(new BigDecimal("1"));
        detail1.setTa_oeb004("1.1.1");
        detail1.setOeb04("1605531313005");
        detail1.setOeb06("自定义物料描述001");
        detail1.setTa_oeb008("自定义图号标准号001");
        detail1.setTa_oeb009("自定义规格尺寸001");
        detail1.setOeb11("客户产品型号描述001");
        try {
            detail1.setOeb15(new SimpleDateFormat("yyyy-MM-dd").parse("2022-12-31"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        detail1.setOeb12(new BigDecimal("28"));
        detail1.setOeb05(new BigDecimal("1")); // "只"转换为数值
        detail1.setOeb13(new BigDecimal("1287.000000"));
        detail1.setOeb14(new BigDecimal("31890.270000"));
        detail1.setOeb14t(new BigDecimal("36036.000000"));
        detail1.setTa_oeb001(new BigDecimal("1287.000"));
        detail1.setTa_oeb006(new BigDecimal("36036.00"));
        detail1.setTa_oeb002(new BigDecimal("1287.000"));
        detail1.setTa_oeb007(new BigDecimal("36036.00"));
        detail1.setOebud01("行备注行备注001");
        detail1.setTa_oeb003("001");
        detail1.setTa_oeb005("客户管道位号00001");

        details.add(detail1);

        // 第二行明细
        SalesOrderDetailDTO detail2 = new SalesOrderDetailDTO();
        detail2.setOeb03(new BigDecimal("2"));
        detail2.setTa_oeb004("2.2.2");
        detail2.setOeb04("1605731313005");
        detail2.setOeb06("自定义物料描述002");
        detail2.setTa_oeb008("自定义图号标准号002");
        detail2.setTa_oeb009("自定义规格尺寸002");
        detail2.setOeb11("客户产品型号描述002");
        try {
            detail2.setOeb15(new SimpleDateFormat("yyyy-MM-dd").parse("2022-12-31"));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        detail2.setOeb12(new BigDecimal("17"));
        detail2.setOeb05(new BigDecimal("1")); // "只"转换为数值
        detail2.setOeb13(new BigDecimal("2187.000000"));
        detail2.setOeb14(new BigDecimal("32901.770000"));
        detail2.setOeb14t(new BigDecimal("37179.000000"));
        detail2.setTa_oeb001(new BigDecimal("2187.000"));
        detail2.setTa_oeb006(new BigDecimal("37179.00"));
        detail2.setTa_oeb002(new BigDecimal("2187.000"));
        detail2.setTa_oeb007(new BigDecimal("37179.00"));
        detail2.setOebud01("行备注行备注002");
        detail2.setTa_oeb003("001");
        detail2.setTa_oeb005("客户管道位号00002");

        details.add(detail2);

        // 3. 构造请求并发送
        SalesOrderResponseDTO result = salesOrderService.sendSalesOrderRequest(salesOrderDTO, details);

        // 4. 处理响应
        System.out.println("响应结果: " + result.getDescription());
        System.out.println("响应状态: " + result.getStatus());
        System.out.println("ERP单号: " + result.getErpSalesOrderCode());
    }
}
