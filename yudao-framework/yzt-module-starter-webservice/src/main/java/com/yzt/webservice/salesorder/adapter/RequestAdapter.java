package com.yzt.webservice.salesorder.adapter;

import com.yzt.webservice.salesorder.model.Request;
import com.yzt.webservice.salesorder.util.XmlUtils;

import javax.xml.bind.annotation.adapters.XmlAdapter;

public class RequestAdapter extends XmlAdapter<String, Request> {
    @Override
    public String marshal(Request request) throws Exception {
        // 只替换 < 为 &lt;，保留 > 不变
        return XmlUtils.marshalToString(request.getRequestWrapper());
    }

    @Override
    public Request unmarshal(String s) throws Exception {
        throw new UnsupportedOperationException("Unmarshal not supported");
    }
}
