package com.yzt.webservice.salesorder.config;

import com.sun.xml.bind.marshaller.NamespacePrefixMapper;
import com.yzt.webservice.salesorder.model.*;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 销售订单WebServiceConfig配置类
 */
@Configuration
public class SalesOrderWebServiceConfig {

    @Bean
    public Jaxb2Marshaller jaxb2Marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setPackagesToScan("com.yzt.webservice.salesorder.model"); // 替换为你的包名
        return marshaller;
    }


    @Bean
    public HttpComponentsMessageSender messageSender() {
        HttpComponentsMessageSender messageSender = new HttpComponentsMessageSender();

        // 配置认证信息 - 使用正确的用户名和密码
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials("tiptop", "") // 根据实际情况修改密码
        );

        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create()
                .setDefaultCredentialsProvider(credentialsProvider)
                .disableContentCompression(); // 禁用压缩可能有助于调试

        messageSender.setHttpClient(httpClientBuilder.build());
        return messageSender;
    }

    @Bean
    public WebServiceTemplate webServiceTemplate() {
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();

        // 创建并配置 Jaxb2Marshaller
        jaxb2Marshaller.setClassesToBeBound(
                Envelope.class,
                Header.class,
                Body.class,
                CreateRequest.class,
                Request.class,
                Access.class,
                Authentication.class,
                Connection.class,
                Organization.class,
                Locale.class,
                RequestContent.class,
                Parameter.class,
                Record.class,
                Field.class,
                Document.class,
                RecordSet.class,
                Master.class,
                Detail.class,
                CreateResponse.class
        );
        // 设置命名空间前缀映射
        Map<String, Object> marshallerProperties = new HashMap<>();
        marshallerProperties.put("com.sun.xml.bind.namespacePrefixMapper",
                new NamespacePrefixMapper() {
                    @Override
                    public String getPreferredPrefix(String namespaceUri, String suggestion, boolean requirePrefix) {
                        if ("http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay".equals(namespaceUri)) {
                            return "tip";
                        } else if ("http://schemas.xmlsoap.org/soap/envelope/".equals(namespaceUri)) {
                            return "soapenv";
                        }
                        return suggestion; // 默认建议前缀
                    }

                });

        jaxb2Marshaller.setMarshallerProperties(marshallerProperties);


        // 构建 WebServiceTemplate
        WebServiceTemplate webServiceTemplate = new WebServiceTemplate();
        webServiceTemplate.setMarshaller(jaxb2Marshaller);
        webServiceTemplate.setUnmarshaller(jaxb2Marshaller);
        webServiceTemplate.setDefaultUri("http://*************:6384/ws/r/aws_ttsrv2?WSDL");

        return webServiceTemplate;
    }
}
