package com.yzt.webservice.salesorder.util;

import com.yzt.webservice.salesorder.model.Request;
import com.yzt.webservice.salesorder.model.RequestWrapper;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;

/**
 * @Description: XML 工具类
 */
public class XmlUtils {

    private static final Jaxb2Marshaller marshaller;

    static {
        try {
            marshaller = new Jaxb2Marshaller();
            marshaller.setClassesToBeBound(Request.class, RequestWrapper.class);
            marshaller.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException("JAXB 初始化失败", e);
        }
    }

    public static String marshalToString(RequestWrapper wrapper) throws Exception {
        StringWriter writer = new StringWriter();
        marshaller.marshal(wrapper, new StreamResult(writer));
        String xml = writer.toString();

        // 移除 XML 声明
        xml = xml.replaceFirst("<\\?xml.*?\\?>", "").trim();

        // 移除外层 <ns2:request> 标签（如果存在）
        xml = xml.replaceAll("^<ns2:request[^>]*>", "").replaceAll("</ns2:request>$", "");

        // 移除命名空间声明（如 xmlns:ns2="..."）
        xml = xml.replaceAll("xmlns:ns2=\"[^\"]+\"", "")
                .replaceAll("ns2:", "");
        return xml;
    }



    public static Request unmarshalFromString(String xml, Class<Request> clazz) throws Exception {
        // 你可以使用 JAXBContext 或 Spring 的 marshaller 来反序列化
        throw new UnsupportedOperationException("Unmarshalling not supported yet");
    }


    public static String toXml(Object object) throws JAXBException {
        JAXBContext context = JAXBContext.newInstance(object.getClass());
        Marshaller marshaller = context.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.FALSE);
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE); // 去掉 <?xml ... ?>

        StringWriter writer = new StringWriter();
        marshaller.marshal(object, writer);
        return writer.toString();
    }
}


