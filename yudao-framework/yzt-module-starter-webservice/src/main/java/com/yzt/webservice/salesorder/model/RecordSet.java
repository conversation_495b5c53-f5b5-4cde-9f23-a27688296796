package com.yzt.webservice.salesorder.model;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "RecordSet")
public class RecordSet {

    @XmlAttribute(name = "id")
    private String id;
    @XmlElement(name = "Master")
    private Master master;
    @XmlElement(name = "Detail")
    private Detail detail;

}
