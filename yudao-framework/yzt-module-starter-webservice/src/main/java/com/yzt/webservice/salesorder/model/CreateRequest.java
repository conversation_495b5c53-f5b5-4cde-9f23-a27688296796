package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "SalesOrderData.createRequest", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateRequest {
    @XmlElement(name = "request", namespace = "http://www.dsc.com.tw/tiptop/TIPTOPServiceGateWay")
    private Request request;
}