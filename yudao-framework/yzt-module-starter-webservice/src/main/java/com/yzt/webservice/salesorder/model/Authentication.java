package com.yzt.webservice.salesorder.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Authentication")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Authentication {
    // 修改为使用@XmlAttribute以匹配XML结构
    @XmlAttribute(name = "user")
    private String user;

    @XmlAttribute(name = "password")
    private String password;
}