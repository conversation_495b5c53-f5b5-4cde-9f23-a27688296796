spring:
  application:
    name: yudao-server

  profiles:
    active: local

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

server:
  servlet:
    encoding:
      enabled: true
      charset: UTF-8 # 必须设置 UTF-8，避免 WebFlux 流式返回（AI 场景）会乱码问题
      force: true

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true
  setting:
    language: zh_cn

# 工作流 Flowable 配置
flowable:
  # 1. false: 默认值，Flowable 启动时，对比数据库表中保存的版本，如果不匹配。将抛出异常
  # 2. true: 启动时会对数据库中所有表进行更新操作，如果表存在，不做处理，反之，自动创建表
  # 3. create_drop: 启动时自动创建表，关闭时自动删除表
  # 4. drop_create: 启动时，删除旧表，再创建新表
  database-schema-update: true # 设置为 false，可通过 https://github.com/flowable/flowable-sql 初始化
  db-history-used: true # flowable6 默认 true 生成信息表，无需手动设置
  check-process-definitions: false # 设置为 false，禁用 /resources/processes 自动部署 BPMN XML 流程
  history-level: audit # full：保存历史数据的最高级别，可保存全部流程相关细节，包括流程流转各节点参数

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
#      id-type: NONE # "智能"模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库 add: 系统表sql已修改成支持自增，与业务表统一
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${yudao.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成
  mapper-locations: classpath*:mapper/**/*Mapper.xml

mybatis-plus-join:
  banner: false # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器MappedStatement缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: 芋道源码 # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  # Producer 配置项
  producer:
    group: ${spring.application.name}_PRODUCER # 生产者分组

spring:
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    # Kafka Producer 配置项
    producer:
      acks: 1 # 0-不应答。1-leader 应答。all-所有 leader 和 follower 应答。
      retries: 3 # 发送失败时，重试发送的次数
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer # 消息的 value 的序列化
    # Kafka Consumer 配置项
    consumer:
      auto-offset-reset: earliest # 设置消费者分组最初的消费进度为 earliest 。可参考博客 https://blog.csdn.net/lishuangzhe7047/article/details/74530417 理解
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: '*'
    # Kafka Consumer Listener 监听器配置
    listener:
      missing-topics-fatal: false # 消费监听接口监听的主题不存在时，默认会报错。所以通过设置为 false ，解决报错

--- #################### AI 相关配置 ####################

spring:
  ai:
    vectorstore: # 向量存储
      redis:
        index: default-index
        prefix: "default:"
    embedding:
      transformer:
        onnx:
          model-uri: https://raw.gitcode.com/yudaocode/yudao-demo/raw/master/yudao-static/ai/model.onnx
        tokenizer:
          uri: https://raw.gitcode.com/yudaocode/yudao-demo/raw/master/yudao-static/ai/tokenizer.json
    qianfan: # 文心一言
      api-key: x0cuLZ7XsaTCU08vuJWO87Lg
      secret-key: R9mYF9dl9KASgi5RUq0FQt3wRisSnOcK
    zhipuai: # 智谱 AI
      api-key: ********************************.3vh9idLJZ2ZhxDEs
    openai: # OpenAI 官方
      api-key: sk-yzKea6d8e8212c3bdd99f9f44ced1cae37c097e5aa3BTS7z
      base-url: https://api.gptsapi.net
    azure: # OpenAI 微软
      openai:
        endpoint: https://eastusprejade.openai.azure.com
        api-key: xxx
    ollama:
      base-url: http://127.0.0.1:11434
      chat:
        model: llama3
    stabilityai:
      api-key: sk-e53UqbboF8QJCscYvzJscJxJXoFcFg4iJjl1oqgE7baJETmx
  cloud:
    ai:
      tongyi: # 通义千问
        tongyi:
          api-key: sk-Zsd81gZYg7

yudao:
  ai:
    deep-seek: # DeepSeek
      enable: true
      api-key: sk-********************************
      model: deepseek-chat
    xinghuo: # 讯飞星火
      enable: true
      appId: 13c8cca6
      appKey: cb6415c19d6162cda07b47316fcb0416
      secretKey: Y2JiYTIxZjA3MDMxMjNjZjQzYzVmNzdh
      model: generalv3.5
    midjourney:
      enable: true
  #    base-url: https://api.holdai.top/mj-relax/mj
      base-url: https://api.holdai.top/mj
      api-key: sk-dZEPiVaNcT3FHhef51996bAa0bC74806BeAb620dA5Da10Bf
      notify-url: http://java.nat300.top/admin-api/ai/image/midjourney/notify
    suno:
      enable: true
  #    base-url: https://suno-55ishh05u-status2xxs-projects.vercel.app
      base-url: http://127.0.0.1:3001

--- #################### 芋道相关配置 ####################

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao
  web:
    admin-ui:
      url: http://localhost/ # Admin 管理后台 UI 的地址
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
  websocket:
    enable: true # websocket的开关
    path: /infra/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 芋道快速开发平台
    description: 提供管理后台、用户 App 的所有功能
    version: ${yudao.info.version}
    url: ${yudao.web.admin-ui.url}
    email: <EMAIL>
    license: MIT
    license-url: https://gitee.com/zhijiantianya/ruoyi-vue-pro/blob/master/LICENSE
  codegen:
    base-package: com.yzt.srm
    db-schemas:
      - public
      - qr
      - srm
      - salesorder
    front-type: 20 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
    unit-test-enable: false # 是否生成单元测试
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取租户，不许带租户编号
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/infra/file/*/get/** # 获取图片，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /admin-api/pay/notify/** # 支付回调通知，不携带租户编号
      - /jmreport/* # 积木报表，无法携带租户编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带租户编号
      - /admin-api/system/oauth2/special-token # 特殊token获取，无法带上租户编号
      - /admin-api/basedata/hyxx/getList  #取行业信息列表
      - /admin-api/basedata/txzgzs/getList  #取体系资格证书
      - /admin-api/basedata/tspzzs/getList  #取特殊凭证证书
      - /admin-api/basedata/czdl/getList  #取材质大类
      - /admin-api/workflow/gyssq/create  #供应商创建
      - /admin-api/base/caliber-info/get-caliber-range    #尺寸范围
      - /admin-api/basedata/lj/getList    #零件
      - /admin-api/base/part-info/get-part-class-list    #部件代码
      - /admin-api/base/texture/list-all-category #获得所有材质大类
      - /admin-api/base/factory/get-factory-list #获得所有材质大类
      - /admin-api/base/valve-type/list #获得阀种信息列表
      - /admin-api/qa/cgpzbj-bjfl/list #查询部件采购控制分类
      - /admin-api/qa/cgpzbj-bjfl/get-bjcgkzfl-list #查询所有部件采购控制分类
      - /app-api/qa/xsdd-tsyq/create #创建销售订单 特殊要求
      - /admin-api/infra/file/upload-id #上传文件
      - /admin-api/basedata/fktj/list
      - /admin-api/basedata/jgtj/list
      - /admin-api/basedata/bz/list
      - /admin-api/basedata/sl/getSlList
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_qywx_log
      - system_sensitive_word
      - system_oauth2_client
      - system_oauth2_special_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - sys_auto_code_rule
      - sys_auto_code_part
      - sys_auto_code_result
      - bpm_activity_address
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
      - bs_zcxz
      - bs_zcflfs
      - bs_zcfl
      - bs_gcxx
      - bs_kcdd
      - bs_kwxx
      - bs_hjxx
      - bs_jldw
      - bs_jldwdz
      - bs_wxyy
      - bs_zjfs
      - bs_hy
      - bs_hbjfl
      - bs_bzxx
      - mt_zczsj
      - mt_zzcxx
      - op_zcydjl
      - op_zcpdjh
      - qa_th_fj
      - op_zcpdqd
      - op_zcwxjl
      - bs_second_zcfl
      - mt_hbjzsj
      - srm_cgz
      - mt_dljzsj
      - op_bpysrk
      - op_sbybjl
      - op_dljkc
      - mt_zcrfid
      - bs_zcfl_zgy
      - bpm_process_definition_info
      - system_users
      - texture_relation
      - texture
      - std_code_spec
      - standard_item_head
      - standard_item_detail
      - texture_standard
      - report_format
      - requirement
      - require_report_format
      - report_template
      - purchase_order
      - order_requirement
      - TC_PMN_V
      - TC_NMT_V
      - TC_OCC_V
      - TC_XMX_V
      - TC_IMA_V
      - TC_APB_V
      - TC_AZJ_V
      - wf_gysxx
      - wf_gysjcxx
      - wf_tz_shd_shxx
      - wf_tz_shd
      - zsj_gysxx
      - zsj_gysxx_pdjl
      - bs_hyxx
      - bs_wwgx
      - qc_inspection_detail
      - qc_sample_size_code
      - qc_inspection_level
      - qc_size_type
      - qc_dim_maint
      - qc_dim_maint_detail
      - qc_inspection_item
      - qc_hardness_conversion
      - qc_hardness_maint
      - qc_defect_category
      - qc_defect_category_detail
      - exchange_rate
      - wf_bjd_ljh
      - wf_bjd_ljh_mx
      - wf_gysxx_gylxr
      - wf_gysxx_tspzzs
      - wf_gysxx_txzgzs
      - wf_gysxx_pdjl
      - wf_gysxx_gfpj
      - wf_cgdd
      - wf_cgdd_tsyq
      - wf_cgdd_wlxx
      - wf_cgdd_zjzx
      - wf_middle_ghfw
      - zsj_gysxx
      - zsj_gysxx_gylxr
      - zsj_gysxx_tspzzs
      - zsj_gysxx_txzgzs
      - zsj_gysxx_gfpj
      - zsj_cgdd
      - zsj_cgdd_wlxx
      - zsj_cgdd_zjzx
      - zsj_gysxx_ghfw
      - wf_jhqqr
      - wf_jhqqr_hfjhq
      - zsj_kc
      - wf_shd
      - wf_shd_lh
      - wf_shd_rt
      - wf_shd_shxx
      - wf_xjb
      - wf_xjb_gys
      - wf_xjb_wlxx
      - wf_xjb_zjxx
      - wf_bjb
      - wf_bjb_wlxx
      - wf_bjb_zjxx
      - bs_cgz
      - wf_bjb_lsbj
      - wf_xmxxjb
      - wf_xmxxjb_gys
      - wf_xmxxjb_xmxx
      - wf_xmxbjb
      - wf_xmxbjb_xmxx
      - wf_lpsq
      - wf_lpsq_kpqd
      - wf_lpsq_fpmx
      - zsj_kpqd
      - bs_gysz
      - bs_gysz_gys
      - bs_sl
      - wf_hpsq
      - wf_hpsq_kpqd
      - wf_hpsq_fpmx
      - wf_gysbg
      - wf_gysbg_gylxr
      - wf_gysbg_tspzzs
      - wf_gysbg_txzgzs
      - wf_gysbg_pdjl
      - bs_txzgzs
      - bs_tspzzs
      - bs_czdl
      - zsj_jhq
      - wf_cgddbg
      - wf_cgddbg_wlxx
      - wf_cgddbg_zc
      - wf_cgddbg_wlxx_zc
      - wf_cgddbg_wlxx_bgmx
      - wf_cgddbg_zjzx
      - wf_cgddbg_zjzx_bgmx
      - users_gysxx
      - rpt_template
      - cgddxx
      - cgdd_shsl
      - wf_ylxjb
      - wf_ylxjb_gys
      - wf_ylxjb_ylxx
      - wf_ylbjb
      - wf_ylbjb_ylxx
      - vb_caliber_info
      - vb_conn_info
      - vb_design_info
      - vb_drive_info
      - vb_inter_texture
      - vb_lang_type
      - vb_press_info
      - vb_test_info
      - vb_texture
      - vb_texture_extend
      - vb_texture_type_desc
      - vb_torque_range
      - vb_valve_class_desc
      - vb_valve_type
      - vb_valve_type_desc
      - vb_material_info
      - vb_part_info
      - vb_part_class
      - vb_valve_type_parts
      - shd_lhsl_history
      - zsj_cgdd_xlh
      - zsj_sxjgk_ww
      - wf_cgdd_xlh
      - wf_cgddbg_xlh
      - bs_cc
      - bs_lj
      - wf_gysxx_ghfw
      - zsj_gysxx_ghfw
      - wf_gysbg_ghfw
      - bs_cc_kj
      - qa_cgsq
      - qa_cgxxjl
      - qa_bjkzcs
      - qa_tspzzs_tsyq
      - vb_factory
      - qa_cgpzcs
      - qa_wwdd_ghfw
      - bs_bj_lhbs
      - qa_cgpzbj_bjfl
      - qa_xsdd_tsyq
      - wf_cgxxjl
      - wf_cgxxjl_jg
      - wf_cgsq
      - wf_cgsq_wlxx
      - wf_bjd_dh
      - wf_bjd_dh_mx
      - qa_cgsq_wlxx
      - qa_bjd_ljh
      - qa_bjd_ljh_wlmx
      - wf_gysxqswj_gyshf
      - wf_gysxqswj_nbfq
      - bs_wjqd
      - bs_pe
      - bs_pez
      - bs_wlh_cgz
      - bpm_activity_address
      - bs_fktj
      - bs_jgtj
      - bs_bz
      - bs_sz
      - srm_xfxx
      - zsj_jgk_cz
      - parts
      - filltype
      - testgrp
      - fields
      - testitems
      - selects
      - testgrpparts
      - testrelation
      - order_main
      - order_detail
      - order_detail_rn
      - order_summary
      - order_parts_summary
      - summaryfields
      - customerinfo
      - customer
      - customer_prj
      - customerinfo_wf
      - bpm_oa_leave
      - order_main_wf
      - order_detail_wf
      - order_detail_wf_rn
      - vb_part_info
      - vb_part_class
      - customer_prj_wf
      - customer_prj
      - bpm_commission
      - bpm_commission_change
      - bpm_commission_change_dt1
      - bpm_commission_dt1
      - bpm_customer
      - bpm_customer_prj
      - bpm_discount_fee
      - bpm_discount_fee_dt1
      - bpm_order_detail
      - bpm_order_detail_rn
      - bpm_order_main
      - bpm_order_summary
      - bpm_order_parts_summary
      - bpm_order_parts_summary_change
      - bpm_payment
      - bpm_payment_dt1
      - bpm_quotation
      - bpm_quotation_dt1
      - bpm_sales_invoice
      - bpm_sales_invoice_dt1
      - bpm_sales_invoice_dt2
      - bpm_sales_invoice_dt3
      - srm_report_template
      - zsj_jgk_ljh
      - zsj_jgk_zl
      - zsj_jgk_dh
      - zsj_jgk_ww
      - qa_xj_cz
      - qa_xj_czgy
      - qa_xj_ljh
      - qa_xj_dh
      - qa_xj_ww
      - qa_xjd_cz
      - qa_xjd_cz_gys
      - qa_xjd_cz_czmx
      - qa_xjd_czgy
      - qa_xjd_czgy_gys
      - qa_xjd_czgy_czmx
      - qa_xjd_ljh
      - qa_xjd_ljh_gys
      - qa_xjd_ljh_wlmx
      - qa_xjd_dh
      - qa_xjd_dh_gys
      - qa_xjd_dh_wlmx
      - qa_xjd_ww
      - qa_xjd_ww_gxmx
      - qa_xjd_ww_gys
      - qa_bjd_dh
      - qa_bjd_dh_dhmx
      - qa_bjd_cz
      - qa_bjd_cz_czmx
      - qa_bjd_ww
      - qa_bjd_wwmx
      - wf_bjd_cz
      - wf_bjd_cz_mx
      - wf_bjd_ww
      - wf_bjd_ww_mx
      - bs_mj_gy
      - qa_bjd_czgy
      - qa_bjd_czgy_czmx
      - wf_bjd_czgy
      - wf_bjd_czgy_mx
      - zsj_xjk_cz
      - bs_qgdlx_cgdlx
      - qa_cggf
      - qa_cggf_mx
      - zsj_xjk_ljh
      - zsj_xjk_ww
      - qa_cgswtk
      - mould_info
      - wf_mould_purchase
      - wf_mould_transfer
      - wf_mould_transfer_mx
      - wf_mould_repair
      - wf_mould_scrap
      - wf_mould_scrap_details
      - system_object_operation
      - srm_cgzz
      - srm_gcxx
      - wf_gysxx_djlc
      - wf_gysxx_company
      - wf_gysbg_company
      - zsj_gysxx_company
      - bs_djgy
      - bs_bank
      - zsj_sxjgk_ljh
      - zsj_sxjgk_cz
      - qc_sampling_plan
      - qc_sampling_plan_detail
      - rt_type
      - wf_pcs_apply
      - wf_pcs_apply_mx
      - rt_pcs_apply
      - rt_pcs_apply_mx
      - rt_settle_supplier
      - rt_wall_thick
      - rt_wall_norm
      - rt_price_ap_mx
      - rt_price_ap
      - wf_price_ap_mx
      - wf_price_ap
      - rt_pcs_ap
      - rt_pcs_mat
      - rt_pcs_wt
      - wf_pcs_ap
      - wf_pcs_mat
      - wf_pcs_wt
      - wf_settle_proof
      - wf_settle
      - wf_settle_pcs
      - wf_settle_result
      - rt_settle_proof
      - rt_settle
      - rt_settle_pcs
      - rt_settle_result
      - rt_wt_price_mx
      - rt_wt_price
      - wf_wt_price_mx
      - wf_wt_price
      - rt_unsettled_wt
      - rt_unsettled
      - rt_check_item
      - bs_recv_term
      - order_category
    ignore-caches:
      - user_role_ids
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  trade:
    order:
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

debug: false

#minio 配置
minio:
  endpoint: http://*************:8063 #ip地址
  access-key: admin #  账号
  secret-key: a@Qs#2}a1w<aj #  密码
  secure: false #如果是true，则用的是 https而不是http,默认值是true
  module:
    qms: "qms"  # qms桶的名字 相当于文件夹

#object-log记录数据变更日志
object-log:
  host: http://127.0.0.1:8888
  path: /objectlog/add
  threadPool:
    corePoolSize: 5
    maxPoolSize: 10
    queueCapacity: 100
    keepAliveTime: 60
    threadNamePrefix: object-log-thread-
  header:
    - Authorization